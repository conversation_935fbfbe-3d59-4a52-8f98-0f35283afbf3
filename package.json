{"name": "myapp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint:rules": "eslint . --cache", "lint:code-format": "prettier --check \"{src,__mocks__}/**/*.{js,json,md,ts,tsx,yml,yaml}\"", "lint:type-check": "tsc", "lint": "yarn lint:rules && yarn lint:code-format && yarn lint:type-check", "lint:fix": "yarn lint:rules --fix && yarn lint:code-format --write && yarn lint:type-check", "test:report": "jest --collectCoverage --coverageDirectory=\"./coverage\" --ci --reporters=default --reporters=jest-junit --coverage", "pod-install": "npx pod-install"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.24.0", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-native-firebase/storage": "^22.2.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.1", "@react-navigation/stack": "^7.2.5", "@tanstack/react-query": "^5.71.3", "firebase": "^11.9.1", "i18next": "^24.2.3", "intl-pluralrules": "^2.0.1", "ky": "^1.8.0", "react": "19.0.0", "react-error-boundary": "^5.0.0", "react-i18next": "^15.4.1", "react-native": "0.78.2", "react-native-date-picker": "^5.0.12", "react-native-gesture-handler": "^2.25.0", "react-native-image-crop-picker": "^0.50.1", "react-native-image-picker": "^8.2.1", "react-native-mmkv": "^3.2.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.17.2", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "4.10.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-webview": "^13.15.0", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.26.10", "@eslint/js": "^9.23.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.78.2", "@react-native/eslint-config": "0.78.2", "@react-native/metro-config": "0.78.2", "@react-native/typescript-config": "0.78.2", "@testing-library/react-native": "^13.2.0", "@types/jest": "^29.5.13", "@types/node": "^22.13.17", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "@typescript-eslint/parser": "^8.29.0", "babel-plugin-inline-dotenv": "^1.7.0", "babel-plugin-module-resolver": "^5.0.0", "babel-plugin-root-import": "^6.6.0", "dotenv": "^16.4.7", "eslint": "^9.23.0", "eslint-config-prettier": "^10.1.1", "eslint-import-resolver-typescript": "^4.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.2", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-unicorn": "^58.0.0", "jest": "^29.7.0", "prettier": "^3.5.3", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "19.0.0", "typescript": "^5.8.2", "typescript-eslint": "^8.29.0"}, "engines": {"node": ">=20"}}