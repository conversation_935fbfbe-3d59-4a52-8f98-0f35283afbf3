{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"types": ["react-native", "jest", "node"], "paths": {"@/*": ["./src/*"]}, "noUnusedLocals": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["tests/**/*", "./src/**/*", "index.d.ts", "*.mjs", ".*.mjs", "*.js", ".*.js", "*.ts", ".prettierrc.mjs"], "exclude": ["node_modules", "_", "android", "ios"]}