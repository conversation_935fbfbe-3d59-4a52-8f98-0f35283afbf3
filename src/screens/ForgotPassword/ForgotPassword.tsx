import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TextInput,
    TouchableOpacity,
    Alert,
    ActivityIndicator,
    ScrollView,
    KeyboardAvoidingView,
    Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { sendPasswordReset } from '@/config/authService';
import { rm } from '@/utils/scaling';

const Colors = {
    PrimaryColor: '#3674B5',
    AccentColor: '#ffc107',
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Border: '#dee2e6',
    Success: '#28a745',
    Error: '#dc3545',
    Info: '#17a2b8',
};

const ForgotPassword: React.FC = () => {
    const navigation = useNavigation();
    const [email, setEmail] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [emailSent, setEmailSent] = useState<boolean>(false);
    const [emailError, setEmailError] = useState<string>('');

    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    const handleEmailChange = (text: string) => {
        setEmail(text);
        setEmailError('');

        if (text.trim() && !validateEmail(text.trim())) {
            setEmailError('Please enter a valid email address');
        }
    };

    const handleSendResetEmail = async () => {
        if (!email.trim()) {
            Alert.alert('Email Required', 'Please enter your email address.');
            return;
        }

        if (!validateEmail(email.trim())) {
            Alert.alert('Invalid Email', 'Please enter a valid email address.');
            return;
        }

        try {
            setLoading(true);
            const result = await sendPasswordReset(email.trim());
            if (result.success) {
                setEmailSent(true);
                Alert.alert(
                    'Reset Email Sent',
                    'A password reset link has been sent to your email address. Please check your inbox and follow the instructions to reset your password.',
                    [
                        {
                            text: 'OK',
                            onPress: () => navigation.goBack(),
                        },
                    ]
                );
            } else {
                Alert.alert('Reset Failed', result.error || 'Failed to send reset email. Please try again.');
            }
        } catch (error: any) {
            console.error('Password reset error:', String(error));
            Alert.alert('Reset Failed', 'An unexpected error occurred. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleBackToLogin = () => {
        navigation.goBack();
    };

    return (
        <KeyboardAvoidingView
            style={styles.container}
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        >
            <ScrollView
                contentContainerStyle={styles.scrollContainer}
                showsVerticalScrollIndicator={false}
                keyboardShouldPersistTaps="handled"
            >
                {/* Header */}
                <View style={styles.header}>
                    <TouchableOpacity
                        style={styles.backButton}
                        onPress={handleBackToLogin}
                    >
                        <Icon name="arrow-back" size={24} color={Colors.Dark} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Forgot Password</Text>
                    <View style={styles.headerSpacer} />
                </View>

                {/* Content */}
                <View style={styles.content}>
                    {/* Icon */}
                    <View style={styles.iconContainer}>
                        <Icon name="mail-outline" size={80} color={Colors.PrimaryColor} />
                    </View>

                    {/* Title and Description */}
                    <Text style={styles.title}>Reset Your Password</Text>
                    <Text style={styles.description}>
                        Enter your email address and we'll send you a link to reset your password.
                    </Text>

                    {/* Email Input */}
                    <View style={styles.inputContainer}>
                        <Text style={styles.inputLabel}>Email Address</Text>
                        <View style={[
                            styles.inputWrapper,
                            emailError ? styles.inputWrapperError : null
                        ]}>
                            <Icon name="mail-outline" size={20} color={Colors.Gray} style={styles.inputIcon} />
                            <TextInput
                                style={styles.textInput}
                                placeholder="Enter your email address"
                                placeholderTextColor={Colors.Gray}
                                value={email}
                                onChangeText={handleEmailChange}
                                keyboardType="email-address"
                                autoCapitalize="none"
                                autoCorrect={false}
                                editable={!loading}
                            />
                        </View>
                        {emailError ? (
                            <Text style={styles.errorText}>{emailError}</Text>
                        ) : null}
                    </View>

                    {/* Send Reset Email Button */}
                    <TouchableOpacity
                        style={[
                            styles.resetButton,
                            (loading || !email.trim() || emailError || !validateEmail(email.trim())) && styles.resetButtonDisabled
                        ]}
                        onPress={handleSendResetEmail}
                        disabled={loading || !email.trim() || emailError || !validateEmail(email.trim())}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color={Colors.White} />
                        ) : (
                            <Text style={styles.resetButtonText}>Send Reset Email</Text>
                        )}
                    </TouchableOpacity>

                    {/* Back to Login */}
                    <TouchableOpacity
                        style={styles.backToLoginButton}
                        onPress={handleBackToLogin}
                    >
                        <Icon name="arrow-back" size={16} color={Colors.PrimaryColor} />
                        <Text style={styles.backToLoginText}>Back to Login</Text>
                    </TouchableOpacity>

                    {/* Help Text */}
                    <View style={styles.helpContainer}>
                        <Text style={styles.helpText}>
                            Remember your password?{' '}
                            <Text style={styles.helpLink} onPress={handleBackToLogin}>
                                Sign in here
                            </Text>
                        </Text>
                    </View>
                </View>
            </ScrollView>
        </KeyboardAvoidingView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.White,
    },
    scrollContainer: {
        flexGrow: 1,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: rm(20),
        paddingTop: rm(50),
        paddingBottom: rm(20),
        backgroundColor: Colors.White,
    },
    backButton: {
        width: rm(40),
        height: rm(40),
        borderRadius: rm(20),
        backgroundColor: Colors.LightGray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    headerSpacer: {
        width: rm(40),
    },
    content: {
        flex: 1,
        paddingHorizontal: rm(30),
        paddingTop: rm(20),
    },
    iconContainer: {
        alignItems: 'center',
        marginBottom: rm(30),
    },
    title: {
        fontSize: rm(28),
        fontWeight: 'bold',
        color: Colors.Heading,
        textAlign: 'center',
        marginBottom: rm(12),
    },
    description: {
        fontSize: rm(16),
        color: Colors.Gray,
        textAlign: 'center',
        lineHeight: rm(24),
        marginBottom: rm(40),
    },
    inputContainer: {
        marginBottom: rm(24),
    },
    inputLabel: {
        fontSize: rm(14),
        fontWeight: '600',
        color: Colors.Dark,
        marginBottom: rm(8),
    },
    inputWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.Border,
        borderRadius: rm(12),
        backgroundColor: Colors.White,
        paddingHorizontal: rm(16),
        height: rm(50),
    },
    inputWrapperError: {
        borderColor: Colors.Error,
    },
    inputIcon: {
        marginRight: rm(12),
    },
    textInput: {
        flex: 1,
        fontSize: rm(16),
        color: Colors.Dark,
        paddingVertical: 0,
    },
    resetButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(12),
        height: rm(50),
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: rm(24),
    },
    resetButtonDisabled: {
        backgroundColor: Colors.Gray,
        opacity: 0.6,
    },
    resetButtonText: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: Colors.White,
    },
    backToLoginButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: rm(12),
        marginBottom: rm(30),
    },
    backToLoginText: {
        fontSize: rm(16),
        color: Colors.PrimaryColor,
        fontWeight: '600',
        marginLeft: rm(8),
    },
    helpContainer: {
        alignItems: 'center',
        marginTop: rm(20),
    },
    helpText: {
        fontSize: rm(14),
        color: Colors.Gray,
        textAlign: 'center',
    },
    helpLink: {
        color: Colors.PrimaryColor,
        fontWeight: '600',
    },
    errorText: {
        fontSize: rm(12),
        color: Colors.Error,
        marginTop: rm(6),
        marginLeft: rm(4),
    },
});

export default ForgotPassword;
