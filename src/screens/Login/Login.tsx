import { ScrollView, Text, TouchableOpacity, View, Alert, ActivityIndicator, StyleSheet } from 'react-native';
import { Paths } from '@/navigation/paths';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native-paper';
import type { RootScreenProps } from '@/navigation/types';
import { useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';


// Import your Firebase auth instance (Web SDK)
import { auth, db } from '@/config/firebase';
import { signInWithEmailAndPassword } from 'firebase/auth';

// --- START: MMKV Import ---
// Import your MMKV storage instance. Adjust the path if App.js is not two levels up.
import { storage } from '../../App';
import { doc, getDoc } from 'firebase/firestore';
// --- END: MMKV Import ---

function Login({ navigation }: RootScreenProps<Paths.Login>) {
    const { layout, backgrounds } = useTheme();
    const { t } = useTranslation();

    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);

    const handleLogin = async () => {
        if (!email || !password) {
            Alert.alert('Error', 'Please enter your email and password.');
            return;
        }

        setLoading(true);

        try {
            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;
            // Fetch user data from Firestore
            const userDocRef = doc(db, "users", user.uid);
            const userDocSnap = await getDoc(userDocRef);

            let firestoreUserData = {};

            if (userDocSnap.exists()) {
                firestoreUserData = userDocSnap.data();
            } else {
                console.warn('No additional user data found in Firestore.');
            }

            // --- START: Save user response to MMKV storage ---
            try {
                // Prepare user data to save. Only include what's necessary and serializable.
                const userDataToSave = {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName,
                    photoURL: user.photoURL,
                    emailVerified: user.emailVerified,
                    ...firestoreUserData,
                    // Add any other simple, non-circular properties you need from the user object
                };
                // MMKV stores strings, booleans, numbers. Objects need to be stringified.
                await AsyncStorage.setItem('myUser', JSON.stringify(userDataToSave));

            } catch (mmkvError) {
                console.error('Failed to save user data to MMKV:', mmkvError);
                // Decide how to handle this error: does it prevent login? Probably not critical.
            }
            // --- END: Save user response to MMKV storage ---

            // --- IMPORTANT NAVIGATION NOTE ---
            // While you can call `navigation.navigate(Paths.Role)` here,
            // the more robust and recommended way with Firebase Auth is to let
            // your global `onAuthStateChanged` listener (in `AuthProvider` in `App.js`)
            // handle the navigation by reacting to the Firebase auth state change.
            // This ensures your app's navigation state always reflects the true auth status.
            // For example, if a user's token becomes invalid later, the listener will
            // automatically redirect them to the login screen.
            // For immediate transition after login, `navigation.navigate` works,
            // but the `onAuthStateChanged` is the ultimate authority for auth flow.

            // The navigation to Paths.Role will happen due to AuthContext update in App.js
            // If you want an immediate, explicit navigation here that might override
            // the App.js conditional rendering for a moment, you can keep it:
            // navigation.navigate(Paths.Role);

        } catch (error: any) {
            console.error("Login Error:", error);

            let errorMessage = 'An unexpected error occurred during login. Please try again.';
            if (error.code) {
                switch (error.code) {
                    case 'auth/invalid-email':
                        errorMessage = 'That email address is invalid!';
                        break;
                    case 'auth/user-disabled':
                        errorMessage = 'This user account has been disabled.';
                        break;
                    case 'auth/user-not-found':
                        errorMessage = 'No user found with that email address.';
                        break;
                    case 'auth/wrong-password':
                        errorMessage = 'Incorrect password.';
                        break;
                    default:
                        errorMessage = error.message;
                }
            }
            Alert.alert('Login Error', errorMessage);

        } finally {
            setLoading(false);
        }
    };

    return (
        <ScrollView
            contentContainerStyle={{
                flexGrow: 1,
                justifyContent: 'center',
            }}
            keyboardShouldPersistTaps="handled"
            bounces={false}
        >
            <View style={[
                layout.flex_1,
                layout.col,
                layout.justifyCenter,
                backgrounds.white
            ]}>
                <View style={{ alignItems: 'center', justifyContent: 'center', }}>
                    <AssetByVariant path="Logo" resizeMode="contain" style={{ marginBottom: 30 }} />
                    <AssetByVariant path="Welcome" resizeMode="contain" />
                </View>

                <TextInput
                    label="Email"
                    value={email}
                    onChangeText={setEmail}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    style={{ marginHorizontal: 20, marginTop: 10, borderRadius: 8, backgroundColor: "white", height: 50, }}
                    activeUnderlineColor='black'
                    underlineColor='transparent'
                    contentStyle={{ color: "black", borderWidth: 1, borderColor: "#DDDDDD", borderRadius: 8, }}
                />
                <TextInput
                    label="Password"
                    value={password}
                    onChangeText={setPassword}
                    secureTextEntry={true}
                    style={{ marginHorizontal: 20, marginTop: 10, borderRadius: 8, backgroundColor: "white", height: 50, }}
                    activeUnderlineColor='black'
                    underlineColor='transparent'
                    contentStyle={{ color: "black", borderWidth: 1, borderColor: "#DDDDDD", borderRadius: 8, }}
                />

                <View style={{ marginVertical: 30, marginHorizontal: 20, flexDirection: 'row', }}>
                    <Text>
                        Don't have an account?
                    </Text>
                    <TouchableOpacity onPress={() => navigation.navigate(Paths.Register)}>
                        <Text style={{ fontSize: 14, color: "#3674B5", fontWeight: '600' }}> {t('common.registerNow')}</Text>
                    </TouchableOpacity>
                </View>

                <TouchableOpacity
                    style={{ justifyContent: 'center', alignItems: 'center', height: 50, marginHorizontal: 20, borderRadius: 8, backgroundColor: "#3674B5", marginBottom: 10 }}
                    onPress={handleLogin}
                    disabled={loading}
                >
                    {loading ? (
                        <ActivityIndicator color="white" />
                    ) : (
                        <Text style={{ fontSize: 14, color: "white", fontWeight: '600', }}>
                            {t('common.login')}
                        </Text>
                    )}
                </TouchableOpacity>

                <TouchableOpacity
                    style={{ marginTop: 10, marginHorizontal: 20, alignItems: 'center' }}
                    onPress={() => navigation.navigate(Paths.ForgotPassword)}
                >
                    <Text style={{ fontSize: 14, color: "#3674B5", fontWeight: '600' }}>
                        {t('common.forgotPassword')}
                    </Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    )
}

export default Login;