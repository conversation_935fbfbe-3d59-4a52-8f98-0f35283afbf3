import React, { useEffect, useState, useMemo } from 'react'; // Added useMemo
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Image,
    TouchableOpacity,

    FlatList,
    ActivityIndicator,
    Alert,
    SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { WebView } from 'react-native-webview';

// Firebase Imports
import { getFirestore, doc, getDoc, Timestamp } from 'firebase/firestore';
import { app, auth } from '@/config/firebase'; // Adjust this path if necessary
import { Linking } from 'react-native'
import AsyncStorage from '@react-native-async-storage/async-storage';
import { convertTime } from '@/config/FirebaseDateFormat';
import { RootScreenProps } from '@/navigation/types';
import { Paths } from '@/navigation/paths';
import { checkDockBookingStatus, BookingStatus, isSlotBooked } from '@/utils/bookingUtils';
import { getDockReviews, getDockReviewStats, canUserReviewDock, Review, ReviewStats } from '@/utils/reviewUtils';
import { rm } from '@/utils/scaling';



// Mock Colors for demonstration
const Colors = {
    PrimaryColor: '#3674B5',
    AccentColor: '#ffc107',
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Approved: '#28a745',
    Border: '#dee2e6',
};

// Initialize Firestore
const db = getFirestore(app);

declare global {
    namespace ReactNavigation {
        interface RootParamList {
            SpecificDockViewScreen: { dockId: string };
        }
    }
}

// Define the route prop type for this screen
type SpecificDockViewScreenRouteProp = RouteProp<ReactNavigation.RootParamList, 'SpecificDockViewScreen'>;


function SpecificDockViewScreen({ navigation }: RootScreenProps<Paths.ViewDock>) {
    const route = useRoute<SpecificDockViewScreenRouteProp>();
    const { dockId } = route.params || {};

    // --- State variables for Dock Details ---
    const [dockData, setDockData] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [userData, setUserData] = useState<any>(null);

    const [isOwner, setIsOwner] = useState(false);
    const [bookingStatus, setBookingStatus] = useState<BookingStatus>({
        isBooked: false,
        bookedSlots: [],
        availableSlots: [],
        totalSlots: 0,
        remainingSlots: 0
    });

    // Review-related state
    const [reviews, setReviews] = useState<Review[]>([]);
    const [reviewStats, setReviewStats] = useState<ReviewStats>({
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    });
    const [canAddReview, setCanAddReview] = useState<boolean>(false);
    const [reviewsLoading, setReviewsLoading] = useState<boolean>(true);
    const user = auth.currentUser;

    // Single date selection state for ViewDock
    const [selectedDate, setSelectedDate] = useState<string | null>(null);
    const [selectedSlots, setSelectedSlots] = useState<string[]>([]);

    // Get available dates from dock data (only dates that have time slots)
    const availableDates = useMemo(() => {
        if (dockData?.dateTimeSlots && typeof dockData.dateTimeSlots === 'object') {
            // Get dates that have time slots defined
            return Object.keys(dockData.dateTimeSlots)
                .filter(dateString => {
                    const slots = dockData.dateTimeSlots[dateString];
                    return Array.isArray(slots) && slots.length > 0;
                })
                .sort(); // Sort dates chronologically
        }

        return [];
    }, [dockData?.dateTimeSlots]);
    useEffect(() => {
        const fetchDockDetails = async () => {
            if (!dockId) {
                setError('No dock ID provided.');
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                setError(null);
                const docRef = doc(db, 'docks', dockId);
                const docSnap = await getDoc(docRef);
                const myUser = await AsyncStorage.getItem('myUser');
                setUserData(myUser != null ? JSON.parse(myUser) : null);



                if (docSnap.exists()) {
                    const data = docSnap.data();

                    // Check if current user is the owner of this specific dock
                    const isDockOwner = !!(user && data.userId === user.uid);
                    setIsOwner(isDockOwner);

                    // Process dateTimeSlots
                    let processedDateTimeSlots: { [key: string]: any[] } = {};

                    // Handle dateTimeSlots format
                    if (data.dateTimeSlots && typeof data.dateTimeSlots === 'object') {
                        console.log("Processing dateTimeSlots:", data.dateTimeSlots);

                        // Process date-specific time slots
                        Object.entries(data.dateTimeSlots).forEach(([dateString, slots]: [string, any]) => {
                            if (Array.isArray(slots)) {
                                processedDateTimeSlots[dateString] = slots.map((slot: any) => ({
                                    id: slot.id,
                                    startTime: slot.startTime instanceof Timestamp ? slot.startTime.toDate() : null,
                                    endTime: slot.endTime instanceof Timestamp ? slot.endTime.toDate() : null,
                                }));
                            }
                        });

                        console.log("Processed dateTimeSlots:", processedDateTimeSlots);
                    } else {
                        console.log("No dateTimeSlots found in dock data");
                    }

                    setDockData({
                        ...data,
                        dockId,
                        dateTimeSlots: processedDateTimeSlots,
                    });
                } else {
                    setError('Dock not found.');
                }
            } catch (err: any) {
                console.error('Error fetching dock details:', err);
                setError(`Failed to load dock details: ${err.message || 'Unknown error'}`);
            } finally {
                setLoading(false);
            }
        };

        fetchDockDetails();
    }, [dockId]);

    // Fetch booking status when dock data is loaded
    useEffect(() => {
        const fetchBookingStatus = async () => {
            if (dockData && dockData.dateTimeSlots) {
                // Get all time slots from all dates
                const allTimeSlots = Object.values(dockData.dateTimeSlots).flat();
                if (allTimeSlots.length > 0) {
                    const status = await checkDockBookingStatus(dockId, allTimeSlots);
                    setBookingStatus(status);
                }
            }
        };

        fetchBookingStatus();
    }, [dockId, dockData?.dateTimeSlots]); // Depend on dateTimeSlots

    // Fetch reviews and check review eligibility when dock data is loaded
    useEffect(() => {
        const fetchReviewsAndEligibility = async () => {
            if (!dockId) return;

            try {
                setReviewsLoading(true);

                // Fetch reviews and stats
                const [reviewsData, statsData] = await Promise.all([
                    getDockReviews(dockId),
                    getDockReviewStats(dockId)
                ]);

                setReviews(reviewsData);
                setReviewStats(statsData);

                // Check if current user can add a review (only for customers)
                if (user && !isOwner) {
                    const eligibility = await canUserReviewDock(user.uid, dockId);
                    setCanAddReview(eligibility.canReview);
                }

            } catch (error) {
                console.error('Error fetching reviews:', error);
            } finally {
                setReviewsLoading(false);
            }
        };

        fetchReviewsAndEligibility();
    }, [dockId, user, isOwner]);

    // Add focus effect to refresh role and reviews when screen comes into focus
    useFocusEffect(
        React.useCallback(() => {
            const refreshData = async () => {
                console.log('ViewDock screen focused, refreshing data...');


                // Re-check ownership if we have dock data
                let currentIsOwner = isOwner; // Use current state as default
                if (dockData && user) {
                    const isDockOwner = !!(user && dockData.userId === user.uid);
                    setIsOwner(isDockOwner);
                    currentIsOwner = isDockOwner; // Update local variable

                }

                // Refresh reviews when returning to screen (e.g., after adding a review)
                if (dockId) {
                    try {
                        const [reviewsData, statsData] = await Promise.all([
                            getDockReviews(dockId),
                            getDockReviewStats(dockId)
                        ]);

                        setReviews(reviewsData);
                        setReviewStats(statsData);

                        // Re-check review eligibility using the current ownership status
                        if (user && !currentIsOwner) {
                            const eligibility = await canUserReviewDock(user.uid, dockId);
                            setCanAddReview(eligibility.canReview);
                        }
                    } catch (error) {
                        console.error('Error refreshing reviews:', error);
                    }
                }
            };

            refreshData();
        }, [dockData, user, dockId, isOwner])
    );

    // Get available time slots for a specific date
    const getTimeSlotsForDate = (dateString: string) => {
        if (dockData?.dateTimeSlots && dockData.dateTimeSlots[dateString]) {
            return dockData.dateTimeSlots[dateString];
        }

        return [];
    };

    // Format date for display
    const formatDateForDisplay = (dateString: string) => {
        const date = new Date(dateString + 'T12:00:00');
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            day: '2-digit',
            month: 'short'
        });
    };

    // Handle date selection (single date for ViewDock)
    const handleDatePress = (dateString: string) => {
        if (selectedDate === dateString) {
            // Deselect current date
            setSelectedDate(null);
            setSelectedSlots([]);
        } else {
            // Select new date
            setSelectedDate(dateString);
            setSelectedSlots([]); // Clear previous slot selections
        }
    };

    // Handle slot selection for the selected date
    const handleSlotPress = (slotId: string) => {
        // Don't allow selection of booked slots
        if (isSlotBooked(slotId, bookingStatus.bookedSlots)) {
            return;
        }

        // Ensure a date is selected first
        if (!selectedDate) {
            return;
        }

        setSelectedSlots(prev => {
            if (prev.includes(slotId)) {
                // Remove slot
                return prev.filter(id => id !== slotId);
            } else {
                // Add slot
                return [...prev, slotId];
            }
        });
    };

    // Handle adding a review
    const handleAddReview = () => {
        if (!canAddReview) {
            Alert.alert(
                'Cannot Add Review',
                'You must have a completed booking to review this dock.'
            );
            return;
        }

        navigation.navigate(Paths.AddReview, {
            dockId: dockData.dockId,
            dockName: dockData.name,
            dockImage: dockData.imageUrls?.[0]
        });
    };

    const renderStar = (rating: number) => {
        const stars = [];
        const normalizedRating = Math.max(0, Math.min(5, Number(rating) || 0));
        for (let i = 1; i <= 5; i++) {
            stars.push(
                <Text key={i} style={normalizedRating >= i ? styles.starFilled : styles.starEmpty}>
                    ★
                </Text>
            );
        }
        return <View style={styles.starsContainer}>{stars}</View>;
    };

    const renderReviewItem = ({ item }: { item: Review }) => (
        <View style={styles.reviewCard}>
            {renderStar(item.rating)}
            <Text style={styles.reviewText}>{item.comment}</Text>
            <View style={styles.reviewerInfo}>
                <Image
                    source={{ uri: `https://placehold.co/40x40/adb5bd/ffffff?text=${item.userName.charAt(0).toUpperCase()}` }}
                    style={styles.reviewerImage}
                />
                <View>
                    <Text style={styles.reviewerName}>{item.userName}</Text>
                    <Text style={styles.reviewerLocation}>
                        {item.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}
                    </Text>
                </View>
            </View>
        </View>
    );

    // Use useMemo to generate the HTML for the WebView only when dockData changes
    const mapEmbedHtml = useMemo(() => {
        if (!dockData || !dockData.address || !dockData.city || !dockData.state) {
            // Return an empty or placeholder HTML if address data is missing
            return `
        <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: #6c757d; font-family: sans-serif;">
          Address data is incomplete to display map.
        </div>
      `;
        }

        // IMPORTANT: To get a direct map pointer without a search panel,
        // you typically need Latitude and Longitude coordinates.
        // Geocoding (converting address to lat/lng) usually requires a Google Maps API Key
        // and is best done on the backend or during data entry.
        // For this example, if dockData does NOT have lat/lng, we'll use a search query,
        // which *might* still show some search UI depending on how accurate Google's resolution is.

        const addressForMap = `${dockData.address}, ${dockData.city}, ${dockData.state}, ${dockData.zipCode}`;

        // Replace YOUR_GOOGLE_MAPS_API_KEY with your actual API key
        // You would typically store this securely (e.g., in environment variables)
        const GOOGLE_MAPS_API_KEY = 'YOUR_GOOGLE_MAPS_API_KEY'; // <<<--- REPLACE THIS

        // If you have latitude and longitude in dockData (preferred for direct pointer)
        const latitude = dockData.latitude; // Assume dockData has these fields
        const longitude = dockData.longitude; // Assume dockData has these fields

        let mapHtmlContent = '';

        if (latitude && longitude) {
            // Option 1: Using Google Maps JavaScript API with Lat/Lng (Best for pointer-only)
            mapHtmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
          <style>
            html, body, #map {
              height: 100%;
              margin: 0;
              padding: 0;
            }
          </style>
          <script>
            function initMap() {
              const map = new google.maps.Map(document.getElementById('map'), {
                zoom: 15, // Adjust zoom level as needed
                center: { lat: ${latitude}, lng: ${longitude} },
                disableDefaultUI: true, // Hides most UI controls (zoom, pan, street view)
                // You can selectively enable controls like:
                // zoomControl: true,
                // mapTypeControl: true,
                // streetViewControl: true,
                // fullscreenControl: true,
              });

              new google.maps.Marker({
                position: { lat: ${latitude}, lng: ${longitude} },
                map: map,
                title: "${dockData.name || 'Dock Location'}"
              });
            }
          </script>
          <script src="https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&callback=initMap" async defer></script>
        </head>
        <body>
          <div id="map"></div>
        </body>
        </html>
      `;
        } else {
            // Option 2: Fallback to Google Maps embed URL with a query (May show search UI)
            // This is the one that was showing the "View map" button.
            // This is used if you *don't* have lat/lng directly.
            const encodedAddress = encodeURIComponent(addressForMap);
            mapHtmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta name="viewport" content="initial-scale=1.0, user-scalable=no">
          <style>
            html, body { height: 100%; margin: 0; padding: 0; }
            iframe { width: 100%; height: 100%; border: none; }
          </style>
        </head>
        <body>
          <iframe src="https://maps.google.com/maps?q=${encodedAddress}&z=15&t=m&output=embed"></iframe>
        </body>
        </html>
      `;
            // You can try to hide elements with injectedJavaScript on this iframe,
            // but it's less reliable for a clean "pointer-only" view.
            console.warn("WARN: Latitude/Longitude not found in dockData. Falling back to less precise map embed. This may show search UI.");
        }

        return mapHtmlContent;
    }, [dockData]); // Re-generate HTML if dockData changes


    if (loading) {
        return (
            <View style={styles.centeredContainer}>
                <ActivityIndicator size="large" color={Colors.PrimaryColor} />
                <Text style={{ marginTop: rm(10), color: Colors.Gray }}>Loading dock details...</Text>
            </View>
        );
    }

    if (error) {
        return (
            <View style={styles.centeredContainer}>
                <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
                <TouchableOpacity style={styles.retryButton} onPress={() => { /* navigation.goBack() or fetchDockDetails() */ }}>
                    <Text style={styles.retryButtonText}>Go Back / Try Again</Text>
                </TouchableOpacity>
            </View>
        );
    }

    if (!dockData) {
        return (
            <View style={styles.centeredContainer}>
                <Text style={{ color: Colors.Gray }}>No dock data available.</Text>
            </View>
        );
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                {/* Header */}


                <ScrollView style={styles.scrollViewContent} showsVerticalScrollIndicator={false}>
                    {/* Hero Image */}
                    <View style={styles.heroContainer}>
                        <View style={styles.header}>

                            {isOwner && (
                                <TouchableOpacity
                                    style={styles.editButton}
                                    onPress={() => navigation.navigate(Paths.DockDetail as any, { dockId: dockData.dockId })}
                                >
                                    <Icon name="pencil" size={18} color="#3674B5" />
                                    <Text style={styles.editButtonText}>Edit</Text>
                                </TouchableOpacity>
                            )}
                        </View>
                        <Image
                            source={{ uri: dockData.imageUrls?.[0] || 'https://i0.wp.com/www.ecomena.org/wp-content/uploads/2020/02/enviroment-friendly-docks.jpg' }}
                            style={styles.heroImage}
                            resizeMode="cover"
                        />
                    </View>

                    {/* Content Container */}
                    <View style={styles.contentContainer}>
                        {/* Dock Header */}
                        <View style={styles.dockHeader}>
                            <View style={styles.titleSection}>
                                <Text style={styles.dockName}>{dockData.name || 'Dock Name'}</Text>
                                <Text style={styles.dockLocation}>
                                    {`${dockData.address || ''}, ${dockData.city || ''}, ${dockData.state || ''} ${dockData.zipCode || ''}`.trim().replace(/^,\s*/, '')}
                                </Text>
                            </View>

                            {!isOwner && (
                                <View style={styles.actionSection}>
                                    <TouchableOpacity style={styles.likeButton}>
                                        <Icon name="heart-outline" size={20} color="#DC3545" />
                                    </TouchableOpacity>
                                    {reviewStats.totalReviews > 0 && (
                                        <View style={styles.ratingContainer}>
                                            <Icon name="star" size={16} color="#FFD700" />
                                            <Text style={styles.ratingText}>
                                                {reviewStats.averageRating.toFixed(1)}
                                            </Text>
                                        </View>
                                    )}
                                </View>
                            )}
                        </View>
                        {/* Dock Specifications */}
                        <View style={styles.specsSection}>
                            <Text style={styles.sectionTitle}>Specifications</Text>
                            <View style={styles.specsGrid}>
                                <View style={styles.specItem}>
                                    <Icon name="resize-outline" size={20} color="#3674B5" />
                                    <View style={styles.specContent}>
                                        <Text style={styles.specLabel}>Dimensions (Length X Width)</Text>
                                        <Text style={styles.specValue}>
                                            {dockData.length || 'N/A'}' × {dockData.width || 'N/A'}'
                                        </Text>
                                    </View>
                                </View>
                                <View style={styles.specItem}>
                                    <Icon name="water-outline" size={20} color="#3674B5" />
                                    <View style={styles.specContent}>
                                        <Text style={styles.specLabel}>Depth</Text>
                                        <Text style={styles.specValue}>{dockData.depth || 'N/A'} M</Text>
                                    </View>
                                </View>
                                <View style={styles.specItem}>
                                    <Icon name="boat-outline" size={20} color="#3674B5" />
                                    <View style={styles.specContent}>
                                        <Text style={styles.specLabel}>Type</Text>
                                        <Text style={styles.specValue}>{dockData.type || 'N/A'}</Text>
                                    </View>
                                </View>
                            </View>
                        </View>

                        {/* Description Section */}
                        <View style={styles.descriptionSection}>
                            <Text style={styles.sectionTitle}>About This Dock</Text>
                            <View style={styles.descriptionContainer}>
                                <Text style={styles.dockDescription}>
                                    {dockData.description || 'No description available for this dock.'}
                                </Text>
                            </View>
                        </View>

                        {/* Rating Badge - Only show if there are reviews */}
                        {reviewStats.totalReviews > 0 && (
                            <View style={styles.badgeSection}>
                                <View style={styles.bestRatedBadge}>
                                    <Icon name="star" size={16} color="#FFD700" />
                                    <Text style={styles.bestRatedText}>
                                        {reviewStats.averageRating >= 4.5 ? 'Highly Rated Dock' : 'Rated Dock'}
                                    </Text>
                                </View>
                                <View style={styles.ratingDetails}>
                                    <Text style={styles.ratingScore}>
                                        {reviewStats.averageRating.toFixed(1)}
                                    </Text>
                                    <Text style={styles.ratingCount}>
                                        Based on {reviewStats.totalReviews} review{reviewStats.totalReviews !== 1 ? 's' : ''}
                                    </Text>
                                </View>
                            </View>
                        )}

                        {/* Multi-Date Availability Section */}
                        <View style={styles.availabilitySection}>
                            <Text style={styles.sectionTitle}>Select Dates & Time Slots</Text>
                            <Text style={styles.sectionSubtitle}>Choose multiple dates and time slots for your booking</Text>

                            {/* Date Selection */}
                            <Text style={styles.subsectionTitle}>Available Dates</Text>
                            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.datesScrollView}>
                                <View style={styles.datesContainer}>
                                    {availableDates.map((dateString) => {
                                        const isSelected = selectedDate === dateString;
                                        return (
                                            <TouchableOpacity
                                                key={dateString}
                                                style={[
                                                    styles.dateButton,
                                                    isSelected && styles.dateButtonSelected,
                                                ]}
                                                onPress={() => handleDatePress(dateString)}
                                                activeOpacity={0.8}
                                            >
                                                <Text style={[
                                                    styles.dateButtonText,
                                                    isSelected && styles.dateButtonTextSelected
                                                ]}>
                                                    {formatDateForDisplay(dateString)}
                                                </Text>
                                            </TouchableOpacity>
                                        );
                                    })}
                                </View>
                            </ScrollView>

                            {/* Time Slots for Selected Date */}
                            {selectedDate && (
                                <>
                                    <Text style={styles.subsectionTitle}>Time Slots</Text>
                                    <View style={styles.dateSlotSection}>
                                        <Text style={styles.dateSlotTitle}>
                                            {new Date(selectedDate + 'T12:00:00').toLocaleDateString('en-US', {
                                                weekday: 'long',
                                                day: '2-digit',
                                                month: 'long',
                                                year: 'numeric'
                                            })}
                                        </Text>

                                        {(() => {
                                            const availableSlots = getTimeSlotsForDate(selectedDate);

                                            if (!availableSlots || availableSlots.length === 0) {
                                                return (
                                                    <View style={styles.noSlotsContainer}>
                                                        <Icon name="time-outline" size={rm(32)} color={Colors.Gray} />
                                                        <Text style={styles.noSlotsText}>No time slots available for this date</Text>
                                                    </View>
                                                );
                                            }

                                            return (
                                                <View style={styles.timeSlotsGrid}>
                                                    {availableSlots.map((slot: any, index: number) => {
                                                        const slotIsBooked = isSlotBooked(slot.id, bookingStatus.bookedSlots);
                                                        const isSelected = selectedSlots.includes(slot.id);

                                                        return (
                                                            <TouchableOpacity
                                                                key={`${selectedDate}-${slot.id || index}`}
                                                                style={[
                                                                    styles.timeSlotCard,
                                                                    isSelected && styles.timeSlotCardSelected,
                                                                    slotIsBooked && styles.timeSlotCardBooked
                                                                ]}
                                                                onPress={() => handleSlotPress(slot.id)}
                                                                disabled={slotIsBooked}
                                                                activeOpacity={0.8}
                                                            >
                                                                {isSelected && !slotIsBooked && (
                                                                    <View style={styles.selectedIndicator}>
                                                                        <Icon name="checkmark-circle" size={rm(16)} color="#FFFFFF" />
                                                                    </View>
                                                                )}

                                                                <Text style={[
                                                                    styles.timeSlotTime,
                                                                    isSelected && styles.timeSlotTimeSelected,
                                                                    slotIsBooked && styles.timeSlotTimeBooked
                                                                ]}>
                                                                    {slot.startTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                                </Text>
                                                                <Text style={[
                                                                    styles.timeSlotSeparator,
                                                                    isSelected && styles.timeSlotSeparatorSelected,
                                                                    slotIsBooked && styles.timeSlotSeparatorBooked
                                                                ]}>to</Text>
                                                                <Text style={[
                                                                    styles.timeSlotTime,
                                                                    isSelected && styles.timeSlotTimeSelected,
                                                                    slotIsBooked && styles.timeSlotTimeBooked
                                                                ]}>
                                                                    {slot.endTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                                </Text>
                                                                <View style={styles.timeSlotPriceContainer}>
                                                                    <Text style={[
                                                                        styles.timeSlotPrice,
                                                                        isSelected && styles.timeSlotPriceSelected,
                                                                        slotIsBooked && styles.timeSlotPriceBooked
                                                                    ]}>${slot.price || dockData.price || 0}</Text>
                                                                    <Text style={[
                                                                        styles.timeSlotPriceLabel,
                                                                        isSelected && styles.timeSlotPriceLabelSelected,
                                                                        slotIsBooked && styles.timeSlotPriceLabelBooked
                                                                    ]}>per slot</Text>
                                                                    <View style={[
                                                                        styles.timeSlotBadge,
                                                                        isSelected && styles.timeSlotBadgeSelected,
                                                                        slotIsBooked && styles.timeSlotBadgeBooked
                                                                    ]}>
                                                                        <Text style={[
                                                                            styles.timeSlotBadgeText,
                                                                            isSelected && styles.timeSlotBadgeTextSelected,
                                                                            slotIsBooked && styles.timeSlotBadgeTextBooked
                                                                        ]}>
                                                                            {slotIsBooked ? 'Not Available' : isSelected ? 'Selected' : 'Available'}
                                                                        </Text>
                                                                    </View>
                                                                </View>
                                                            </TouchableOpacity>
                                                        );
                                                    })}
                                                </View>
                                            );
                                        })()}
                                    </View>
                                </>
                            )}
                        </View>

                        {/* Managed By */}
                        <View style={styles.managedByContainer}>
                            <View style={styles.managedByInfo}>
                                <Image source={{ uri: 'https://placehold.co/50x50/ced4da/ffffff?text=M' }} style={styles.managedByImage} />
                                <View>
                                    <Text style={styles.managedByText}>{user?.displayName}</Text>
                                    <Text>Joined in {convertTime(userData?.createdAt)?.toString() || ''}</Text>
                                </View>
                            </View>
                            <TouchableOpacity onPress={() => Linking.openURL(`tel:${userData?.phoneNumber}`)
                            } style={styles.phoneButton}>
                                <Text style={styles.phoneIcon}>📞</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Reviews Section */}
                    <View style={[styles.card, { marginTop: rm(10), paddingHorizontal: 0 }]}>
                        <View style={styles.reviewsHeader}>
                            <View style={styles.reviewsTitleContainer}>
                                <Text style={styles.reviewsTitle}>Reviews</Text>
                                {reviewStats.totalReviews > 0 && renderStar(reviewStats.averageRating)}
                            </View>
                            {reviewStats.totalReviews > 0 && (
                                <View style={styles.reviewStatsContainer}>
                                    <Text style={styles.averageRating}>
                                        {reviewStats.averageRating.toFixed(1)}
                                    </Text>
                                    <Text style={styles.totalReviews}>
                                        ({reviewStats.totalReviews} review{reviewStats.totalReviews !== 1 ? 's' : ''})
                                    </Text>
                                </View>
                            )}
                        </View>

                        {reviewsLoading ? (
                            <View style={styles.reviewsLoadingContainer}>
                                <ActivityIndicator size="small" color={Colors.PrimaryColor} />
                                <Text style={styles.loadingText}>Loading reviews...</Text>
                            </View>
                        ) : reviews.length > 0 ? (
                            <FlatList
                                data={reviews}
                                renderItem={renderReviewItem}
                                keyExtractor={(item) => item.id}
                                horizontal
                                showsHorizontalScrollIndicator={false}
                                contentContainerStyle={styles.reviewsListContainer}
                            />
                        ) : (
                            <View style={styles.noReviewsContainer}>
                                <Icon name="chatbubble-outline" size={32} color={Colors.Gray} />
                                <Text style={styles.noReviewsText}>No reviews yet</Text>
                                <Text style={styles.noReviewsSubtext}>
                                    Be the first to share your experience!
                                </Text>
                            </View>
                        )}

                        {/* Add Review Button - Only show for customers who can review */}
                        {!isOwner && (
                            <TouchableOpacity
                                style={[
                                    styles.addReviewButton,
                                    !canAddReview && styles.addReviewButtonDisabled
                                ]}
                                onPress={handleAddReview}
                                disabled={!canAddReview}
                            >
                                <Text style={[
                                    styles.addReviewButtonText,
                                    !canAddReview && styles.addReviewButtonTextDisabled
                                ]}>
                                    {canAddReview ? 'Add your Review' : 'Booking Required to Review'}
                                </Text>
                            </TouchableOpacity>
                        )}
                    </View>

                    {/* Where you'll be */}
                    <View style={[styles.card, { marginTop: rm(10) }]}>
                        <Text style={styles.sectionTitle}>Where you'll be</Text>
                        {/* WebView for embedding the map */}
                        {(dockData.address && dockData.city && dockData.state) ? (
                            <WebView
                                style={styles.mapWebView} // Apply specific styles for the WebView
                                originWhitelist={['*']} // Allow all origins for the map
                                source={{ html: mapEmbedHtml }} // Load HTML content
                                javaScriptEnabled={true}
                                // It's less effective to inject CSS to hide elements that are part of the
                                // maps.google.com/maps?output=embed UI, as they are not typically hidden easily.
                                // The primary solution is to use Google Maps JavaScript API with lat/lng.
                                // injectedJavaScript={`
                                //   const style = document.createElement('style');
                                //   style.innerHTML = \`
                                //     .section-hero-header-image { display: none !important; }
                                //     .section-hero-action-button { display: none !important; }
                                //     .maps-widget-pane-info-button { display: none !important; }
                                //   \`;
                                //   document.head.appendChild(style);
                                //   true; // required for injectedJavaScript
                                // `}
                                // Show loading indicator inside WebView if needed
                                renderLoading={() => (
                                    <ActivityIndicator size="small" color={Colors.PrimaryColor} style={{ flex: 1 }} />
                                )}
                                startInLoadingState={true}
                                scrollEnabled={false} // Prevent scrolling within the WebView if you want a fixed map
                            />
                        ) : (
                            <View style={styles.mapPlaceholder}>
                                <Text style={styles.mapPlaceholderText}>Address not available to display map.</Text>
                            </View>
                        )}
                    </View>
                    {/* Spacer to ensure content above bottom bar is scrollable */}
                    <View style={{ height: rm(80) }} />
                </ScrollView>

                {/* Bottom Booking Bar - Only show for customers */}
                {!isOwner && (
                    <View style={styles.bottomBar}>
                        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <Text style={styles.priceText}>${(dockData.price || 0).toFixed(2)}</Text>
                            <Text style={styles.priceUnit}> / slot</Text>
                        </View>
                        <TouchableOpacity style={styles.bookNowButton} onPress={() => {
                            navigation.navigate(Paths.DockBooking, {
                                dockData: {
                                    ...dockData,
                                    preSelectedDate: selectedDate,
                                    preSelectedSlots: selectedSlots
                                },
                                userData: userData
                            });
                        }}>
                            <Text style={styles.bookNowButtonText}>Book Now</Text>
                        </TouchableOpacity>
                    </View>
                )}

            </View >
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.White,
    },
    centeredContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.White,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'flex-end',
        // paddingHorizontal: rm(20),
        // paddingTop: rm(50),
        // paddingBottom: rm(15),
        backgroundColor: "transparent",
        // i want this over image
        // position: 'absolute',
    },
    backButton: {
        width: rm(40),
        height: rm(40),
        borderRadius: rm(20),
        backgroundColor: '#F8F9FA',
        justifyContent: 'center',
        alignItems: 'center',
    },
    editButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#E3F2FD',
        borderRadius: rm(20),
        paddingHorizontal: rm(16),
        paddingVertical: rm(8),
        //over the image
        position: 'absolute',
        zIndex: 10,
        top: 10,
        right: 10
    },
    editButtonText: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#3674B5',
        marginLeft: rm(6),
    },
    heroContainer: {
        height: rm(280),
        width: '100%',
        backgroundColor: '#F8F9FA',
    },
    heroImage: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    contentContainer: {
        flex: 1,
        backgroundColor: Colors.White,
        borderTopLeftRadius: rm(24),
        borderTopRightRadius: rm(24),
        marginTop: rm(-20),
        paddingTop: rm(24),
        paddingHorizontal: rm(20),
    },
    dockHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: rm(24),
    },
    titleSection: {
        flex: 1,
        marginRight: rm(16),
    },
    dockName: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(4),
        lineHeight: rm(30),
    },
    dockLocation: {
        fontSize: rm(16),
        color: '#6C757D',
        lineHeight: rm(22),
    },
    actionSection: {
        alignItems: 'flex-end',
    },
    likeButton: {
        width: rm(44),
        height: rm(44),
        borderRadius: rm(22),
        backgroundColor: '#FFF5F5',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: rm(8),
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF8E1',
        borderRadius: rm(12),
        paddingHorizontal: rm(8),
        paddingVertical: rm(4),
    },
    ratingText: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#F57C00',
        marginLeft: rm(4),
    },
    specsSection: {
        marginBottom: rm(24),
    },
    sectionTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(16),
    },
    sectionSubtitle: {
        fontSize: rm(14),
        color: '#6C757D',
        marginBottom: rm(20),
    },
    specsGrid: {
        gap: rm(12),
    },
    specItem: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
    },
    specContent: {
        marginLeft: rm(12),
        flex: 1,
    },
    specLabel: {
        fontSize: rm(12),
        color: '#6C757D',
        marginBottom: rm(2),
    },
    specValue: {
        fontSize: rm(16),
        fontWeight: '600',
        color: '#2C3E50',
    },
    availabilitySection: {
        marginBottom: rm(24),
    },
    dateContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
        marginBottom: rm(16),
    },
    dateContent: {
        marginLeft: rm(12),
        flex: 1,
    },
    dateLabel: {
        fontSize: rm(12),
        color: '#6C757D',
        marginBottom: rm(2),
    },
    dateValue: {
        fontSize: rm(16),
        fontWeight: '600',
        color: '#2C3E50',
    },
    timeSlotsScrollContainer: {
        paddingRight: rm(20),
    },
    timeSlotCard: {
        backgroundColor: 'white',
        borderRadius: rm(12),
        padding: rm(12),
        flex: 1,
        marginHorizontal: rm(4),
        marginBottom: rm(8),
        minWidth: rm(110),
        maxWidth: rm(130),
        alignItems: 'center',
        borderWidth: 1,
        borderColor: '#E3F2FD',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 1,
        },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    timeSlotTime: {
        fontSize: rm(11),
        color: '#2C3E50',
        fontWeight: '500',
        textAlign: 'center',
    },
    timeSlotPrice: {
        fontSize: rm(12),
        fontWeight: 'bold',
        color: '#3674B5',
    },
    // Enhanced Description Section
    descriptionSection: {
        marginBottom: rm(24),
    },
    descriptionContainer: {
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
        borderLeftWidth: 4,
        borderLeftColor: '#3674B5',
    },
    // Enhanced Badge Section
    badgeSection: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#FFF8E1',
        borderRadius: rm(12),
        padding: rm(16),
        marginBottom: rm(24),
        borderWidth: 1,
        borderColor: '#FFE082',
    },
    ratingDetails: {
        alignItems: 'flex-end',
    },
    ratingScore: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: '#FF8F00',
    },
    ratingCount: {
        fontSize: rm(12),
        color: '#6C757D',
        marginTop: rm(2),
    },
    // Enhanced Time Slots
    timeSlotsSection: {
        marginTop: rm(16),
    },
    subsectionTitle: {
        fontSize: rm(16),
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: rm(4),
    },
    subsectionSubtitle: {
        fontSize: rm(14),
        color: '#6C757D',
        marginBottom: rm(16),
    },
    timeSlotsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        // justifyContent: 'space-around',
        marginHorizontal: rm(-4),
    },
    timeSlotHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: rm(6),
        width: '100%',
    },
    timeSlotBadge: {
        marginTop: rm(5),
        backgroundColor: '#E8F5E8',
        borderRadius: rm(6),
        paddingHorizontal: rm(4),
        paddingVertical: rm(1),
    },
    timeSlotBadgeText: {
        fontSize: rm(8),
        fontWeight: '600',
        color: '#2E7D32',
    },
    timeSlotSeparator: {
        fontSize: rm(10),
        color: '#6C757D',
        marginVertical: rm(1),
        textAlign: 'center',
    },
    timeSlotPriceContainer: {
        marginTop: rm(6),
        alignItems: 'center',
    },
    timeSlotPriceLabel: {
        fontSize: rm(9),
        color: '#6C757D',
        marginTop: rm(1),
    },
    // Booked Time Slot Styles
    timeSlotCardBooked: {
        backgroundColor: '#F8F9FA',
        borderColor: '#DEE2E6',
        opacity: 0.6,
    },
    timeSlotBadgeBooked: {
        backgroundColor: '#FFEBEE',
    },
    timeSlotBadgeTextBooked: {
        color: '#DC3545',
    },
    scrollViewContent: {
        flex: 1,
    },
    mainImage: {
        width: '100%',
        height: rm(200),
        borderRadius: rm(10),
        marginBottom: rm(10),
    },
    card: {
        backgroundColor: Colors.White,
        borderRadius: rm(10),
        marginHorizontal: rm(10),
        padding: rm(15),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },

    dockAddress: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginBottom: rm(5),
    },
    dockSpecs: {
        marginBottom: rm(10),
    },
    dockSpecText: {
        fontSize: rm(14),
        color: Colors.Dark,
        lineHeight: rm(20),
    },
    boldText: {
        fontWeight: 'bold',
    },
    dockDescription: {
        fontSize: rm(15),
        color: '#2C3E50',
        lineHeight: rm(24),
        textAlign: 'left',
    },
    ratingAndDatesContainer: {
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        paddingTop: rm(15),
        marginBottom: rm(15),
    },
    bestRatedBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF3E0',
        borderRadius: rm(20),
        paddingVertical: rm(8),
        paddingHorizontal: rm(12),
        gap: rm(6),
    },
    bestRatedText: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#FF8F00',
    },
    dateButtonsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: rm(8), // Modern way to add spacing between wrapped items
    },

    managedByContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        paddingTop: rm(15),
        marginTop: rm(15),
    },
    managedByInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    managedByImage: {
        width: rm(50),
        height: rm(50),
        borderRadius: rm(25),
        marginRight: rm(10),
        backgroundColor: Colors.Border, // Placeholder background
    },
    managedByText: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    managedBySubText: {
        fontSize: rm(12),
        color: Colors.Gray,
    },
    phoneButton: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(25),
        width: rm(50),
        height: rm(50),
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 2
    },
    phoneIcon: {
        fontSize: rm(24),
        color: Colors.White,
    },

    reviewsHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: rm(15), // Apply padding here instead of card
        marginBottom: rm(10),
    },
    reviewsTitleContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    reviewsTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginRight: rm(5),
    },
    starsContainer: {
        flexDirection: 'row',
        marginBottom: rm(5),
    },
    starFilled: {
        color: Colors.AccentColor, // Gold color for filled stars
        fontSize: rm(16),
    },
    starEmpty: {
        color: Colors.Gray,
        fontSize: rm(16),
    },
    viewAllText: {
        color: Colors.PrimaryColor,
        fontSize: rm(14),
        fontWeight: 'bold',
    },
    safeArea: {
        flex: 1,
        backgroundColor: 'white',
    },
    reviewsListContainer: {
        paddingHorizontal: rm(15), // Padding for the FlatList items
        paddingBottom: rm(10), // Padding below the reviews
    },
    reviewCard: {
        backgroundColor: Colors.White,
        borderRadius: rm(8),
        width: rm(250), // Fixed width for horizontal scroll cards
        padding: rm(15),
        marginRight: rm(10),
        borderWidth: 1,
        borderColor: Colors.Border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.05,
        shadowRadius: 2,
        elevation: 2,
    },
    reviewText: {
        fontSize: rm(13),
        color: Colors.Dark,
        lineHeight: rm(18),
        marginBottom: rm(10),
    },
    reviewerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    reviewerImage: {
        width: rm(30),
        height: rm(30),
        borderRadius: rm(15),
        marginRight: rm(8),
        backgroundColor: Colors.Border,
    },
    reviewerName: {
        fontSize: rm(13),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    reviewerLocation: {
        fontSize: rm(11),
        color: Colors.Gray,
    },
    addReviewButton: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(20),
        paddingVertical: rm(10),
        paddingHorizontal: rm(20),
        borderWidth: 1,
        borderColor: Colors.PrimaryColor,
        alignSelf: 'center',
        marginTop: rm(10),
        marginBottom: rm(15),
    },
    addReviewButtonText: {
        color: Colors.PrimaryColor,
        fontSize: rm(16),
        fontWeight: 'bold',
    },
    addReviewButtonDisabled: {
        backgroundColor: Colors.LightGray,
        borderColor: Colors.Border,
        opacity: 0.6,
    },
    addReviewButtonTextDisabled: {
        color: Colors.Gray,
    },
    reviewStatsContainer: {
        alignItems: 'flex-end',
    },
    averageRating: {
        fontSize: rm(20),
        fontWeight: 'bold',
        color: Colors.PrimaryColor,
    },
    totalReviews: {
        fontSize: rm(12),
        color: Colors.Gray,
        marginTop: rm(2),
    },
    reviewsLoadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: rm(20),
    },
    loadingText: {
        marginLeft: rm(8),
        fontSize: rm(14),
        color: Colors.Gray,
    },
    noReviewsContainer: {
        alignItems: 'center',
        paddingVertical: rm(30),
        paddingHorizontal: rm(20),
    },
    noReviewsText: {
        fontSize: rm(16),
        fontWeight: '600',
        color: Colors.Gray,
        marginTop: rm(8),
    },
    noReviewsSubtext: {
        fontSize: rm(14),
        color: Colors.Gray,
        textAlign: 'center',
        marginTop: rm(4),
    },
    mapWebView: { // New style for the WebView
        width: '100%',
        height: rm(180), // Keep a fixed height for the map
        borderRadius: rm(10),
        marginTop: rm(5),
        overflow: 'hidden', // Ensure content respects border radius
        borderColor: Colors.Border,
        borderWidth: 1,
    },
    mapPlaceholder: {
        width: '100%',
        height: rm(180),
        borderRadius: rm(10),
        marginTop: rm(5),
        backgroundColor: Colors.LightGray,
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: Colors.Border,
    },
    mapPlaceholderText: {
        color: Colors.Gray,
        textAlign: 'center',
        padding: rm(10),
    },
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.White,
        paddingVertical: rm(15),
        paddingHorizontal: rm(20),
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 5,
        position: 'absolute', // Make it stick to the bottom
        bottom: 0,
        width: '100%',
    },
    priceText: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    priceUnit: {
        fontSize: rm(14),
        color: Colors.Gray,
    },
    bookNowButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(10),
        paddingVertical: rm(15),
        paddingHorizontal: rm(40),
    },
    bookNowButtonText: {
        color: Colors.White,
        fontSize: rm(18),
        fontWeight: 'bold',
    },
    retryButton: {
        marginTop: rm(20),
        backgroundColor: Colors.PrimaryColor,
        paddingVertical: rm(10),
        paddingHorizontal: rm(20),
        borderRadius: rm(8),
    },
    retryButtonText: {
        color: Colors.White,
        fontSize: rm(16),
    },

    timeSlotsTitle: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(10),
    },
    timeSlotsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: rm(8),
    },
    timeSlotChip: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(20),
        paddingVertical: rm(8),
        paddingHorizontal: rm(15),
        borderWidth: 1,
        borderColor: Colors.PrimaryColor,
        marginBottom: rm(5),
    },
    timeSlotText: {
        fontSize: rm(12),
        color: Colors.PrimaryColor,
        fontWeight: '500',
    },
    // Date selection styles
    datesScrollView: {
        marginBottom: rm(20),
    },
    datesContainer: {
        flexDirection: 'row',
        paddingHorizontal: rm(20),
        gap: rm(12),
    },
    dateButton: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(12),
        paddingVertical: rm(12),
        paddingHorizontal: rm(16),
        borderWidth: 1,
        borderColor: Colors.Border,
        minWidth: rm(80),
        alignItems: 'center',
    },
    dateButtonSelected: {
        backgroundColor: Colors.PrimaryColor,
        borderColor: Colors.PrimaryColor,
    },
    dateButtonText: {
        fontSize: rm(12),
        fontWeight: '600',
        color: Colors.Dark,
        textAlign: 'center',
    },
    dateButtonTextSelected: {
        color: Colors.White,
    },
    // Date slot section styles
    dateSlotSection: {
        marginBottom: rm(24),
    },
    dateSlotTitle: {
        fontSize: rm(16),
        fontWeight: '600',
        color: Colors.Heading,
        marginBottom: rm(12),
    },
    // Time slot selection styles
    timeSlotCardSelected: {
        backgroundColor: Colors.PrimaryColor,
        borderColor: Colors.PrimaryColor,
    },
    timeSlotTimeSelected: {
        color: Colors.White,
    },
    timeSlotSeparatorSelected: {
        color: Colors.White,
    },
    timeSlotPriceSelected: {
        color: Colors.White,
    },
    timeSlotPriceLabelSelected: {
        color: Colors.White,
    },
    timeSlotBadgeSelected: {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
    },
    timeSlotBadgeTextSelected: {
        color: Colors.White,
    },
    timeSlotTimeBooked: {
        color: Colors.Gray,
    },
    timeSlotSeparatorBooked: {
        color: Colors.Gray,
    },
    timeSlotPriceBooked: {
        color: Colors.Gray,
    },
    timeSlotPriceLabelBooked: {
        color: Colors.Gray,
    },
    selectedIndicator: {
        position: 'absolute',
        top: rm(8),
        right: rm(8),
        zIndex: 1,
    },
    // No slots container styles
    noSlotsContainer: {
        alignItems: 'center',
        paddingVertical: rm(30),
        paddingHorizontal: rm(20),
    },
    noSlotsText: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginTop: rm(8),
        textAlign: 'center',
    },

});

export default SpecificDockViewScreen;
