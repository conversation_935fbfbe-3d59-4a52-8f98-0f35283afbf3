import React, { useEffect, useState, useMemo } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Image,
    TouchableOpacity,
    TextInput,
    Alert,
    Linking,
    SafeAreaView
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons'; // For back arrow and phone icon
import { convertTime } from '@/config/FirebaseDateFormat';
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import { checkDockBookingStatus, BookingStatus, isSlotBooked } from '@/utils/bookingUtils';
import { rm } from '@/utils/scaling';

// Type for date-slot mapping
type DateSlotsMap = { [date: string]: string[] };

const Colors = {
    PrimaryColor: '#3674B5', // Example blue
    AccentColor: '#ffc107', // Example yellow/gold for best rated
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Approved: '#28a745',
    Border: '#dee2e6',
    InputBackground: '#f1f1f1', // Lighter gray for input backgrounds
    InputOutline: '#ced4da', // Border color for inputs
};

function DockBooking({ navigation }: RootScreenProps<Paths.DockBooking>) {
    // const navigation = useNavigation();
    type DockBookingRouteParams = {
        dockData?: any;
        userData?: any;
    };
    const route = useRoute<RouteProp<Record<string, DockBookingRouteParams>, string>>();
    // Use data passed via route params, fallback to mock data if none
    const dock = route.params?.dockData || {};
    const user = route.params?.userData || {};

    console.log("DockBooking - Received dock data:", dock);
    console.log("DockBooking - dock.dateTimeSlots:", dock?.dateTimeSlots);

    // --- State for Booking Form Inputs ---
    const [fullName, setFullName] = useState('');
    const [contact, setContact] = useState('');
    const [email, setEmail] = useState('');
    const [boatName, setBoatName] = useState('');
    const [boatType, setBoatType] = useState<string | undefined>(""); // Stores selected boat type
    const [boatLength, setBoatLength] = useState('');
    const [boatWidth, setBoatWidth] = useState('');
    const [registrationNumber, setRegistrationNumber] = useState('');
    const [message, setMessage] = useState('');
    const [selectedSlots, setSelectedSlots] = useState<string[]>([]); // To store selected time slots (slot IDs)
    const [selectedDates, setSelectedDates] = useState<string[]>([]); // To store selected dates
    const [selectedDateSlots, setSelectedDateSlots] = useState<DateSlotsMap>({}); // To store slots per date
    const [bookingStatus, setBookingStatus] = useState<BookingStatus>({
        isBooked: false,
        bookedSlots: [],
        availableSlots: [],
        totalSlots: 0,
        remainingSlots: 0
    });

    // Get available time slots from dock data - memoized to prevent infinite re-renders
    const availableTimeSlots = useMemo(() => {
        if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
            // Get all time slots from all dates
            const allSlots: any[] = [];
            Object.values(dock.dateTimeSlots).forEach(dateSlots => {
                if (Array.isArray(dateSlots)) {
                    allSlots.push(...dateSlots);
                }
            });
            return allSlots;
        }
        return [];
    }, [dock.dateTimeSlots]);

    // Get available dates from dock data (only dates that have time slots)
    const availableDates = useMemo(() => {
        console.log("DockBooking - dock.dateTimeSlots:", dock?.dateTimeSlots);
        if (dock?.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
            // Get dates that have time slots defined
            const dates = Object.keys(dock.dateTimeSlots)
                .filter(dateString => {
                    const slots = dock.dateTimeSlots[dateString];
                    return Array.isArray(slots) && slots.length > 0;
                })
                .sort(); // Sort dates chronologically
            console.log("DockBooking - availableDates:", dates);
            return dates;
        }

        console.log("DockBooking - No dateTimeSlots found");
        return [];
    }, [dock?.dateTimeSlots]);

    // Format date for display
    const formatDateForDisplay = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            day: '2-digit',
            month: 'short'
        });
    };

    // Get available time slots for a specific date
    const getTimeSlotsForDate = (dateString: string) => {
        if (dock?.dateTimeSlots && dock.dateTimeSlots[dateString]) {
            return dock.dateTimeSlots[dateString];
        }

        return [];
    };

    // Fetch booking status
    useEffect(() => {
        const fetchBookingStatus = async () => {
            if (availableTimeSlots.length > 0) {
                const status = await checkDockBookingStatus(dock.dockId, availableTimeSlots);
                setBookingStatus(status);
            }
        };
        fetchBookingStatus();
    }, [dock.dockId, availableTimeSlots]);

    // Initialize pre-selected data from ViewDock screen
    useEffect(() => {
        if (dock.preSelectedDate && dock.preSelectedSlots) {
            setSelectedDates([dock.preSelectedDate]);
            setSelectedDateSlots({ [dock.preSelectedDate]: dock.preSelectedSlots });
            setSelectedSlots(dock.preSelectedSlots);
        }
    }, [dock.preSelectedDate, dock.preSelectedSlots]);

    // Handle date selection
    const handleDatePress = (dateString: string) => {
        setSelectedDates(prev => {
            if (prev.includes(dateString)) {
                // Remove date and its slots
                const newDates = prev.filter(d => d !== dateString);
                const newDateSlots = { ...selectedDateSlots };
                delete newDateSlots[dateString];
                setSelectedDateSlots(newDateSlots);

                // Update overall selected slots
                const allSlots = Object.values(newDateSlots).flat();
                setSelectedSlots(allSlots);

                return newDates;
            } else {
                // Add date
                return [...prev, dateString];
            }
        });
    };

    // Handle slot selection for a specific date
    const handleSlotPress = (slotId: string, dateString: string) => {
        // Don't allow selection of booked slots
        if (isSlotBooked(slotId, bookingStatus.bookedSlots)) {
            return;
        }

        // Ensure the date is selected first
        if (!selectedDates.includes(dateString)) {
            return;
        }

        setSelectedDateSlots(prev => {
            const currentDateSlots = prev[dateString] || [];
            const newDateSlots = currentDateSlots.includes(slotId)
                ? currentDateSlots.filter(id => id !== slotId)
                : [...currentDateSlots, slotId];

            const updatedDateSlots = {
                ...prev,
                [dateString]: newDateSlots
            };

            // Update overall selected slots
            const allSlots = Object.values(updatedDateSlots).flat();
            setSelectedSlots(allSlots);

            return updatedDateSlots;
        });
    };

    // Calculate total price based on selected slots
    const calculateTotalPrice = () => {
        const basePrice = parseFloat(dock.price) || 0;
        return basePrice * selectedSlots.length;
    };

    const handleContinue = () => {
        // Basic validation
        if (!fullName || !contact || !email || !boatName || !boatType || !boatLength || !boatWidth || !registrationNumber || selectedSlots.length === 0 || selectedDates.length === 0) {
            Alert.alert('Missing Information', 'Please fill in all required fields and select at least one date and time slot.');
            return;
        }

        navigation.navigate(Paths.ReviewBooking, {
            booking: {
                fullName,
                contact,
                email,
                boatName,
                boatType,
                boatLength,
                boatWidth,
                registrationNumber,
                message,
                selectedSlots,
                selectedDates,
                selectedDateSlots,
            },
            dock: dock, // Pass dock data to the next screen
        });
    };

    return (
        <SafeAreaView style={styles.safeArea}>

            <View style={styles.container}>
                {/* Header */}
                {/* <View style={styles.header}>
                <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
                    <Icon name="arrow-back" size={rm(24)} color={Colors.Dark} />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Booking</Text>
            </View> */}

                <ScrollView contentContainerStyle={styles.scrollViewContent}>
                    {/* Main Dock Image */}
                    <Image
                        source={{ uri: dock.imageUrls?.[0] || 'https://i0.wp.com/www.ecomena.org/wp-content/uploads/2020/02/enviroment-friendly-docks.jpg' }}
                        style={styles.mainImage}
                        resizeMode="cover"
                    />

                    {/* Dock Details Card */}
                    <View style={styles.dockDetailsCard}>
                        <View style={styles.dockTitleContainer}>
                            <Text style={styles.dockName}>{dock.name}</Text>
                            {/* Best Rated Badge */}
                            {/* <View style={styles.bestRatedBadge}>
                                <Text style={styles.bestRatedText}>⭐ Best Rated</Text>
                            </View> */}
                        </View>
                        <Text style={styles.dockAddress}>{`${dock.address}, ${dock.city}`}</Text>
                        <View style={styles.dockSpecs}>
                            <Text style={styles.dockSpecText}>
                                <Text style={styles.boldText}>Dock Depth:</Text> {dock.depth} |{' '}
                                <Text style={styles.boldText}>Length:</Text> {dock.length} |{' '}
                                <Text style={styles.boldText}>Width:</Text> {dock.width}
                            </Text>
                            <Text style={styles.dockSpecText}>
                                <Text style={styles.boldText}>Dock Type:</Text> {dock.type}
                            </Text>
                        </View>

                        {/* Managed By */}
                        <View style={styles.managedByContainer}>
                            <View style={styles.managedByInfo}>
                                <Image source={{ uri: 'https://placehold.co/50x50/ced4da/ffffff?text=M' }} style={styles.managedByImage} />
                                <View>
                                    <Text style={styles.managedByText}>Managed by {user.displayName}</Text>
                                    <Text style={styles.managedBySubText}>Joined in {convertTime(user?.createdAt)?.toString() || ''}</Text>
                                </View>
                            </View>
                            {/* <TouchableOpacity onPress={() => Alert.alert('Call', `Calling ${dock.managedByPhone}`)} style={styles.phoneButton}>
                            <Icon name="call-outline" size={rm(24)} color={Colors.PrimaryColor} />
                        </TouchableOpacity> */}
                            <TouchableOpacity style={styles.phoneButton} onPress={() => Linking.openURL(`tel:${user?.phoneNumber}`)
                            }>
                                <Text style={styles.phoneIcon}>📞</Text>
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Booking Details Section */}
                    <View style={styles.bookingDetailsCard}>
                        <Text style={styles.sectionTitle}>Enter Your Details to Book</Text>

                        <TextInput
                            style={styles.input}
                            placeholder="Full Name"
                            placeholderTextColor={Colors.Gray}
                            value={fullName}
                            onChangeText={setFullName}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Contact"
                            placeholderTextColor={Colors.Gray}
                            keyboardType="phone-pad"
                            value={contact}
                            onChangeText={setContact}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Email"
                            placeholderTextColor={Colors.Gray}
                            keyboardType="email-address"
                            value={email}
                            onChangeText={setEmail}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Enter Boat Name"
                            placeholderTextColor={Colors.Gray}
                            value={boatName}
                            onChangeText={setBoatName}
                        />

                        {/* Boat Type Picker */}
                        <TextInput
                            style={styles.input}
                            placeholder="Enter Boat Type"
                            placeholderTextColor={Colors.Gray}
                            value={boatType}
                            onChangeText={setBoatType}
                        />

                        <TextInput
                            style={styles.input}
                            placeholder="Enter Boat Length in Feet"
                            placeholderTextColor={Colors.Gray}
                            keyboardType="numeric"
                            value={boatLength}
                            onChangeText={setBoatLength}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Enter Boat Width in Feet"
                            placeholderTextColor={Colors.Gray}
                            keyboardType="numeric"
                            value={boatWidth}
                            onChangeText={setBoatWidth}
                        />
                        <TextInput
                            style={styles.input}
                            placeholder="Enter Registration Number"
                            placeholderTextColor={Colors.Gray}
                            value={registrationNumber}
                            onChangeText={setRegistrationNumber}
                        />

                        {/* Multi-Date and Time Slots Selection */}
                        <Text style={styles.sectionTitle}>Pick Your Dates & Time Slots</Text>
                        <Text style={styles.subTitle}>Select multiple dates and time slots for your booking.</Text>

                        {/* Date Selection */}
                        <Text style={styles.subsectionTitle}>Available Dates</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.datesScrollView}>
                            <View style={styles.datesContainer}>
                                {availableDates.map((dateString, index) => {
                                    const isSelected = selectedDates.includes(dateString);
                                    return (
                                        <TouchableOpacity
                                            key={dateString}
                                            style={[
                                                styles.dateButton,
                                                isSelected && styles.dateButtonSelected,
                                            ]}
                                            onPress={() => handleDatePress(dateString)}
                                        >
                                            <Text style={[
                                                styles.dateButtonText,
                                                isSelected && styles.dateButtonTextSelected,
                                            ]}>
                                                {formatDateForDisplay(dateString)}
                                            </Text>
                                        </TouchableOpacity>
                                    );
                                })}
                            </View>
                        </ScrollView>

                        {/* Time Slots for Selected Dates */}
                        {selectedDates.length > 0 && (
                            <>
                                <Text style={styles.subsectionTitle}>Time Slots</Text>
                                {selectedDates.map((dateString) => (
                                    <View key={dateString} style={styles.dateSlotSection}>
                                        <Text style={styles.dateSlotTitle}>
                                            {new Date(dateString).toLocaleDateString('en-US', {
                                                weekday: 'long',
                                                day: '2-digit',
                                                month: 'long',
                                                year: 'numeric'
                                            })}
                                        </Text>
                                        <View style={styles.slotsContainer}>
                                            {(() => {
                                                const availableSlotsForDate = getTimeSlotsForDate(dateString);
                                                return availableSlotsForDate.length > 0 ? (
                                                    availableSlotsForDate.map((slot: any, index: number) => {
                                                        const slotIsBooked = isSlotBooked(slot.id, bookingStatus.bookedSlots);
                                                        const isSelected = selectedDateSlots[dateString]?.includes(slot.id) || false;

                                                        return (
                                                            <TouchableOpacity
                                                                key={`${dateString}-${slot.id || index}`}
                                                                style={[
                                                                    styles.slotButton,
                                                                    isSelected && styles.slotButtonSelected,
                                                                    slotIsBooked && styles.slotButtonDisabled,
                                                                ]}
                                                                onPress={() => handleSlotPress(slot.id, dateString)}
                                                                disabled={slotIsBooked}
                                                            >
                                                                {slotIsBooked && (
                                                                    <View style={styles.bookedBadge}>
                                                                        <Text style={styles.bookedBadgeText}>Not Available</Text>
                                                                    </View>
                                                                )}
                                                                <View style={styles.slotContent}>
                                                                    {isSelected && !slotIsBooked && (
                                                                        <Icon name="checkmark-circle" size={rm(20)} color={Colors.White} />
                                                                    )}
                                                                    <Icon
                                                                        name="time-outline"
                                                                        size={rm(16)}
                                                                        color={
                                                                            slotIsBooked ? Colors.Gray :
                                                                                isSelected ? Colors.White : Colors.PrimaryColor
                                                                        }
                                                                    />
                                                                    <Text style={[
                                                                        styles.slotButtonText,
                                                                        isSelected && styles.slotButtonTextSelected,
                                                                        slotIsBooked && styles.slotButtonTextDisabled,
                                                                    ]}>
                                                                        {slot.startTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })} - {' '}
                                                                        {slot.endTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                                    </Text>
                                                                </View>
                                                            </TouchableOpacity>
                                                        );
                                                    })
                                                ) : (
                                                    <View style={styles.noSlotsContainer}>
                                                        <Icon name="time-outline" size={rm(32)} color={Colors.Gray} />
                                                        <Text style={styles.noSlotsText}>No time slots available for this date</Text>
                                                    </View>
                                                );
                                            })()}
                                        </View>
                                    </View>
                                ))}
                            </>
                        )}

                        {selectedDates.length === 0 && (
                            <View style={styles.selectDatesPrompt}>
                                <Icon name="calendar-outline" size={rm(48)} color={Colors.Gray} />
                                <Text style={styles.selectDatesText}>Select dates above to see available time slots</Text>
                            </View>
                        )}

                        <TextInput
                            style={styles.messageInput}
                            placeholder="Note"
                            placeholderTextColor={Colors.Gray}
                            multiline
                            numberOfLines={4}
                            value={message}
                            onChangeText={setMessage}
                        />
                    </View>

                    {/* Spacer for bottom bar */}
                    <View style={{ height: rm(100) }} />
                </ScrollView>

                {/* Bottom Booking Bar */}
                <View style={styles.bottomBar}>
                    <View style={styles.priceContainer}>
                        <Text style={styles.priceText}>$ {calculateTotalPrice().toFixed(2)}</Text>
                        {/* <Text style={styles.totalText}>
                            {selectedSlots.length > 0 && selectedDates.length > 0
                                ? `${selectedDates.length} date${selectedDates.length > 1 ? 's' : ''}, ${selectedSlots.length} slot${selectedSlots.length > 1 ? 's' : ''}`
                                : 'Select dates & slots'}
                        </Text> */}
                    </View>
                    <TouchableOpacity
                        style={[styles.continueButton, selectedSlots.length === 0 && styles.continueButtonDisabled]}
                        onPress={handleContinue}
                        disabled={selectedSlots.length === 0}
                    >
                        <Text style={[styles.continueButtonText, selectedSlots.length === 0 && styles.continueButtonTextDisabled]}>
                            Continue
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        backgroundColor: Colors.White,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: rm(15),
        paddingVertical: rm(15),
        backgroundColor: Colors.White,
        paddingTop: rm(40), // Adjust for status bar/notch
        borderBottomWidth: 1,
        borderBottomColor: Colors.Border,
    },
    backButton: {
        padding: rm(5),
    },
    headerTitle: {
        fontSize: rm(20),
        fontWeight: 'bold',
        color: Colors.Dark,
        marginLeft: rm(10),
    },
    scrollViewContent: {
        paddingBottom: rm(20), // Add padding for bottom bar
        backgroundColor: Colors.White,
    },
    mainImage: {
        width: '100%',
        height: rm(200),
        marginBottom: rm(10),
    },
    dockDetailsCard: {
        backgroundColor: Colors.White,
        borderRadius: rm(10),
        marginHorizontal: rm(15),
        marginTop: rm(-50), // Overlap with image
        padding: rm(20),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    dockTitleContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: rm(5),
    },
    dockName: {
        fontSize: rm(22),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    bestRatedBadge: {
        backgroundColor: Colors.AccentColor,
        borderRadius: rm(5),
        paddingVertical: rm(5),
        paddingHorizontal: rm(10),
    },
    bestRatedText: {
        fontSize: rm(12),
        fontWeight: 'bold',
        color: Colors.Dark,
    },
    dockAddress: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginBottom: rm(10),
    },
    dockSpecs: {
        marginBottom: rm(15),
    },
    dockSpecText: {
        fontSize: rm(14),
        color: Colors.Dark,
        lineHeight: rm(20),
    },
    boldText: {
        fontWeight: 'bold',
    },
    managedByContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        paddingTop: rm(15),
        marginTop: rm(15),
    },
    managedByInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    managedByImage: {
        width: rm(40),
        height: rm(40),
        borderRadius: rm(20),
        marginRight: rm(10),
        backgroundColor: Colors.Border,
    },
    managedByText: {
        fontSize: rm(14),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    managedBySubText: {
        fontSize: rm(11),
        color: Colors.Gray,
    },
    phoneButton: {
        backgroundColor: Colors.LightGray, // As per image
        borderRadius: rm(25),
        width: rm(40),
        height: rm(40),
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 1, // Add a border if it looks like that in the image
        borderColor: Colors.Border,
    },
    phoneIcon: {
        fontSize: rm(24),
        color: Colors.White,
    },
    bookingDetailsCard: {
        backgroundColor: Colors.White,
        borderRadius: rm(10),
        marginHorizontal: rm(15),
        marginTop: rm(15),
        padding: rm(20),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
    },
    sectionTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(15),
    },
    subTitle: {
        fontSize: rm(13),
        color: Colors.Gray,
        marginBottom: rm(10),
    },
    subsectionTitle: {
        fontSize: rm(16),
        fontWeight: '600',
        color: Colors.Heading,
        marginBottom: rm(12),
        marginTop: rm(16),
    },
    // Date Selection Styles
    datesScrollView: {
        marginBottom: rm(20),
    },
    datesContainer: {
        flexDirection: 'row',
        paddingHorizontal: rm(20),
        gap: rm(12),
    },
    dateButton: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(12),
        paddingVertical: rm(12),
        paddingHorizontal: rm(16),
        borderWidth: 1,
        borderColor: Colors.Border,
        minWidth: rm(80),
        alignItems: 'center',
    },
    dateButtonSelected: {
        backgroundColor: Colors.PrimaryColor,
        borderColor: Colors.PrimaryColor,
    },
    dateButtonText: {
        fontSize: rm(12),
        fontWeight: '600',
        color: Colors.Dark,
        textAlign: 'center',
    },
    dateButtonTextSelected: {
        color: Colors.White,
    },
    // Date Slot Section Styles
    dateSlotSection: {
        marginBottom: rm(24),
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
        marginHorizontal: rm(20),
    },
    dateSlotTitle: {
        fontSize: rm(14),
        fontWeight: '600',
        color: Colors.Heading,
        marginBottom: rm(12),
    },
    selectDatesPrompt: {
        alignItems: 'center',
        paddingVertical: rm(40),
        marginHorizontal: rm(20),
    },
    selectDatesText: {
        fontSize: rm(16),
        color: Colors.Gray,
        marginTop: rm(12),
        textAlign: 'center',
    },
    input: {
        // backgroundColor: Colors.InputBackground,
        borderRadius: rm(8),
        paddingHorizontal: rm(15),
        paddingVertical: rm(12),
        marginBottom: rm(10),
        fontSize: rm(14),
        color: Colors.Dark,
        borderWidth: 1,
        borderColor: Colors.InputOutline,
    },
    pickerContainer: {
        backgroundColor: Colors.InputBackground,
        borderRadius: rm(8),
        marginBottom: rm(10),
        borderWidth: 1,
        borderColor: Colors.InputOutline,
        overflow: 'hidden', // Ensures picker content stays within bounds
    },
    picker: {
        height: rm(50), // Adjust height as needed
        width: '100%',
        color: Colors.Dark,
    },
    pickerItem: {
        fontSize: rm(14), // Styling for actual items
    },
    pickerPlaceholder: {
        color: Colors.Gray, // Styling for the placeholder item
    },
    messageInput: {
        backgroundColor: Colors.InputBackground,
        borderRadius: rm(8),
        paddingHorizontal: rm(15),
        paddingVertical: rm(12),
        marginBottom: rm(10),
        fontSize: rm(14),
        color: Colors.Dark,
        textAlignVertical: 'top', // For multiline input to start text at top
        height: rm(100),
        borderWidth: 1,
        borderColor: Colors.InputOutline,
    },
    slotsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        marginBottom: rm(20),
        gap: rm(4), // Gap for spacing between buttons
        justifyContent: "space-between"
    },
    slotButton: {
        backgroundColor: Colors.LightGray,
        borderRadius: rm(20),
        paddingVertical: rm(10),
        paddingHorizontal: rm(15),
        borderWidth: 1,
        borderColor: Colors.Border,
    },
    slotButtonSelected: {
        backgroundColor: Colors.PrimaryColor,
        borderColor: Colors.PrimaryColor,
    },
    slotButtonText: {
        fontSize: rm(10),
        color: Colors.Dark,

    },
    slotButtonTextSelected: {
        color: Colors.White,
    },
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.White,
        paddingVertical: rm(15),
        paddingHorizontal: rm(20),
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 5,
        position: 'absolute',
        bottom: 0,
        width: '100%',
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'baseline',
    },
    priceText: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    totalText: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginLeft: rm(5),
    },
    continueButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(10),
        paddingVertical: rm(15),
        paddingHorizontal: rm(40),
    },
    continueButtonText: {
        color: Colors.White,
        fontSize: rm(18),
        fontWeight: 'bold',
    },
    continueButtonDisabled: {
        backgroundColor: Colors.Gray,
        opacity: 0.6,
    },
    continueButtonTextDisabled: {
        color: Colors.LightGray,
    },
    slotContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    noSlotsContainer: {
        alignItems: 'center',
        paddingVertical: rm(30),
        width: '100%',
    },
    noSlotsText: {
        fontSize: rm(16),
        color: Colors.Gray,
        marginTop: rm(8),
        textAlign: 'center',
    },
    // Booked Slot Styles
    slotButtonDisabled: {
        backgroundColor: Colors.LightGray,
        opacity: 0.6,
        borderColor: Colors.Gray,
    },
    slotButtonTextDisabled: {
        color: Colors.Gray,
    },
    bookedBadge: {
        backgroundColor: '#FFEBEE',
        borderRadius: rm(8),
        paddingHorizontal: rm(6),
        paddingVertical: rm(2),
        width: rm(60),
        marginBottom: rm(5)
    },
    bookedBadgeText: {
        fontSize: rm(10),
        fontWeight: '600',
        color: '#DC3545',
        textAlign: "center"
    },
});

export default DockBooking;