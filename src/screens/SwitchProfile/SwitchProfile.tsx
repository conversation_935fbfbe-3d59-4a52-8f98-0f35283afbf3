import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    SafeAreaView,
    Alert,
    ActivityIndicator,
    ScrollView,
} from 'react-native';
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import Icon from 'react-native-vector-icons/Ionicons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { CommonActions } from '@react-navigation/native';
import { rm } from '@/utils/scaling';

type Props = RootScreenProps<Paths.SwitchProfile>;

const SwitchProfile: React.FC<Props> = ({ navigation }) => {
    const [currentRole, setCurrentRole] = useState<string>('customer');
    const [loading, setLoading] = useState(true);
    const [switching, setSwitching] = useState(false);

    useEffect(() => {
        loadCurrentRole();
    }, []);

    const loadCurrentRole = async () => {
        try {
            const role = await AsyncStorage.getItem('userRole');
            setCurrentRole(role || 'customer');
        } catch (error) {
            console.error('Error loading user role:', String(error));
        } finally {
            setLoading(false);
        }
    };

    const handleRoleSwitch = () => {
        const targetRole = currentRole === 'owner' ? 'customer' : 'owner';
        const targetRoleDisplay = targetRole === 'owner' ? 'Dock Owner' : 'Customer';

        Alert.alert(
            'Switch Profile',
            `Are you sure you want to switch to ${targetRoleDisplay} mode?`,
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Switch',
                    onPress: () => switchRole(targetRole),
                    style: 'default',
                },
            ]
        );
    };

    const switchRole = async (newRole: string) => {
        try {
            setSwitching(true);

            // Save new role to AsyncStorage
            await AsyncStorage.setItem('userRole', newRole);

            // Update local state
            setCurrentRole(newRole);

            // Small delay to ensure AsyncStorage is updated
            await new Promise(resolve => setTimeout(resolve, 100));

            // Navigate back to dashboard - the focus effect will handle role detection
            navigation.goBack();

            // // Show success message
            // Alert.alert(
            //     'Profile Switched',
            //     `You are now in ${newRole === 'owner' ? 'Dock Owner' : 'Customer'} mode.`,
            //     [{ text: 'OK' }]
            // );

        } catch (error) {
            console.error('Error switching role:', String(error));
            Alert.alert('Error', 'Failed to switch profile. Please try again.');
        } finally {
            setSwitching(false);
        }
    };

    if (loading) {
        return (
            <SafeAreaView style={styles.safeArea}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#3674B5" />
                    <Text style={styles.loadingText}>Loading...</Text>
                </View>
            </SafeAreaView>
        );
    }

    const targetRole = currentRole === 'owner' ? 'customer' : 'owner';
    const targetRoleDisplay = targetRole === 'owner' ? 'Dock Owner' : 'Customer';
    const currentRoleDisplay = currentRole === 'owner' ? 'Dock Owner' : 'Customer';

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView style={styles.container}>
                {/* Current Profile Status */}
                <View style={styles.currentProfileSection}>
                    <View style={styles.currentProfileCard}>
                        <View style={styles.profileIconContainer}>
                            <Icon
                                name={currentRole === 'owner' ? 'business' : 'person'}
                                size={40}
                                color="#3674B5"
                            />
                        </View>
                        <View style={styles.profileInfo}>
                            <Text style={styles.currentRoleLabel}>Current Profile</Text>
                            <Text style={styles.currentRoleText}>{currentRoleDisplay}</Text>
                            <Text style={styles.currentRoleDescription}>
                                {currentRole === 'owner'
                                    ? 'You can manage and rent out your docks'
                                    : 'You can search and book available docks'
                                }
                            </Text>
                        </View>
                    </View>
                </View>

                {/* Switch Profile Section */}
                <View style={styles.switchSection}>
                    <Text style={styles.switchTitle}>Switch Profile</Text>
                    <Text style={styles.switchSubtitle}>
                        Change your profile type to access different features
                    </Text>

                    <TouchableOpacity
                        style={styles.switchCard}
                        onPress={handleRoleSwitch}
                        disabled={switching}
                        activeOpacity={0.7}
                    >
                        <View style={styles.switchCardContent}>
                            <View style={styles.switchIconContainer}>
                                <Icon
                                    name={targetRole === 'owner' ? 'business-outline' : 'person-outline'}
                                    size={32}
                                    color="#28A745"
                                />
                            </View>
                            <View style={styles.switchInfo}>
                                <Text style={styles.switchToLabel}>Switch to</Text>
                                <Text style={styles.switchToRole}>{targetRoleDisplay}</Text>
                                <Text style={styles.switchToDescription}>
                                    {targetRole === 'owner'
                                        ? 'Start managing and renting out docks'
                                        : 'Browse and book available docks'
                                    }
                                </Text>
                            </View>
                            {switching ? (
                                <ActivityIndicator size="small" color="#28A745" />
                            ) : (
                                <Icon name="arrow-forward" size={24} color="#28A745" />
                            )}
                        </View>
                    </TouchableOpacity>
                </View>

                {/* Information Section */}
                <View style={styles.infoSection}>
                    <View style={styles.infoCard}>
                        <Icon name="information-circle-outline" size={24} color="#3674B5" />
                        <View style={styles.infoContent}>
                            <Text style={styles.infoTitle}>Profile Switching</Text>
                            <Text style={styles.infoText}>
                                You can switch between Customer and Dock Owner profiles at any time.
                                Each profile provides different features and access levels.
                            </Text>
                        </View>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};


const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        paddingHorizontal: rm(20),
        paddingTop: rm(20),
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: rm(16),
        fontSize: rm(16),
        color: '#6C757D',
    },
    currentProfileSection: {
        marginBottom: rm(32),
    },
    currentProfileCard: {
        backgroundColor: 'white',
        borderRadius: rm(16),
        padding: rm(24),
        flexDirection: 'row',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: rm(2),
        },
        shadowOpacity: 0.1,
        shadowRadius: rm(8),
        elevation: 4,
    },
    profileIconContainer: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    profileInfo: {
        flex: 1,
    },
    currentRoleLabel: {
        fontSize: 14,
        color: '#6C757D',
        marginBottom: 4,
    },
    currentRoleText: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 4,
    },
    currentRoleDescription: {
        fontSize: 14,
        color: '#6C757D',
        lineHeight: 20,
    },
    switchSection: {
        marginBottom: 32,
    },
    switchTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 8,
    },
    switchSubtitle: {
        fontSize: 16,
        color: '#6C757D',
        marginBottom: 20,
        lineHeight: 22,
    },
    switchCard: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        borderWidth: 2,
        borderColor: '#28A745',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    switchCardContent: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    switchIconContainer: {
        width: 60,
        height: 60,
        borderRadius: 30,
        backgroundColor: '#E8F5E8',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    switchInfo: {
        flex: 1,
    },
    switchToLabel: {
        fontSize: 14,
        color: '#28A745',
        fontWeight: '600',
        marginBottom: 4,
    },
    switchToRole: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 4,
    },
    switchToDescription: {
        fontSize: 14,
        color: '#6C757D',
        lineHeight: 20,
    },
    infoSection: {
        marginTop: 20,

    },
    infoCard: {
        backgroundColor: 'white',
        borderRadius: 12,
        padding: 16,
        flexDirection: 'row',
        alignItems: 'flex-start',
        borderLeftWidth: 4,
        borderLeftColor: '#3674B5',
    },
    infoContent: {
        flex: 1,
        marginLeft: 12,
    },
    infoTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 4,
    },
    infoText: {
        fontSize: 14,
        color: '#6C757D',
        lineHeight: 20,
    },
});


export default SwitchProfile;

