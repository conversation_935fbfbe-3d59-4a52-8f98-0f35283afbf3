import React, { useState, useEffect } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    TextInput,
    Alert,
    ActivityIndicator,
    Image,
    SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons';
import { auth } from '@/config/firebase';
import { addReview, canUserReviewDock } from '@/utils/reviewUtils';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { rm } from '@/utils/scaling';

const Colors = {
    PrimaryColor: '#3674B5',
    AccentColor: '#ffc107',
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Border: '#dee2e6',
    Success: '#28a745',
    Error: '#dc3545',
};

type AddReviewRouteProp = RouteProp<{
    AddReview: {
        dockId: string;
        dockName: string;
        dockImage?: string;
    };
}, 'AddReview'>;

const AddReview: React.FC = () => {
    const navigation = useNavigation();
    const route = useRoute<AddReviewRouteProp>();
    const { dockId, dockName, dockImage } = route.params;

    const [rating, setRating] = useState<number>(0);
    const [comment, setComment] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(false);
    const [canReview, setCanReview] = useState<boolean>(false);
    const [checkingEligibility, setCheckingEligibility] = useState<boolean>(true);
    const [bookingId, setBookingId] = useState<string>('');
    const [userData, setUserData] = useState<any>(null);

    useEffect(() => {
        checkReviewEligibility();
        loadUserData();
    }, []);

    const loadUserData = async () => {
        try {
            const storedUserData = await AsyncStorage.getItem('myUser');
            if (storedUserData) {
                setUserData(JSON.parse(storedUserData));
            }
        } catch (error) {
            console.error('Error loading user data:', error);
        }
    };

    const checkReviewEligibility = async () => {
        if (!auth.currentUser) {
            Alert.alert('Error', 'You must be logged in to add a review');
            navigation.goBack();
            return;
        }

        try {
            setCheckingEligibility(true);
            const eligibility = await canUserReviewDock(auth.currentUser.uid, dockId);

            if (!eligibility.canReview) {
                Alert.alert(
                    'Cannot Add Review',
                    eligibility.reason || 'You are not eligible to review this dock',
                    [{ text: 'OK', onPress: () => navigation.goBack() }]
                );
                return;
            }

            setCanReview(true);
            setBookingId(eligibility.bookingId || '');
        } catch (error) {
            console.error('Error checking review eligibility:', error);
            Alert.alert('Error', 'Unable to verify review eligibility');
            navigation.goBack();
        } finally {
            setCheckingEligibility(false);
        }
    };

    const handleStarPress = (selectedRating: number) => {
        setRating(selectedRating);
    };

    const renderStars = () => {
        const stars = [];
        for (let i = 1; i <= 5; i++) {
            stars.push(
                <TouchableOpacity
                    key={i}
                    onPress={() => handleStarPress(i)}
                    style={styles.starButton}
                    activeOpacity={0.7}
                >
                    <Icon
                        name={i <= rating ? 'star' : 'star-outline'}
                        size={32}
                        color={i <= rating ? '#FFD700' : '#DEE2E6'}
                    />
                </TouchableOpacity>
            );
        }
        return <View style={styles.starsContainer}>{stars}</View>;
    };

    const handleSubmitReview = async () => {
        if (rating === 0) {
            Alert.alert('Rating Required', 'Please select a rating from 1 to 5 stars');
            return;
        }

        if (!comment.trim()) {
            Alert.alert('Comment Required', 'Please write a comment about your experience');
            return;
        }

        if (!auth.currentUser || !userData) {
            Alert.alert('Error', 'User information not available');
            return;
        }

        try {
            setLoading(true);

            const reviewData = {
                dockId,
                userId: auth.currentUser.uid,
                userName: userData.fullName || auth.currentUser.displayName || 'Anonymous',
                userEmail: userData.email || auth.currentUser.email || '',
                rating,
                comment: comment.trim(),
                bookingId,
            };

            const result = await addReview(reviewData);

            if (result.success) {
                Alert.alert(
                    'Review Added',
                    'Thank you for your review! It has been successfully submitted.',
                    [{ text: 'OK', onPress: () => navigation.goBack() }]
                );
            } else {
                Alert.alert('Error', result.error || 'Failed to add review');
            }
        } catch (error) {
            console.error('Error submitting review:', error);
            Alert.alert('Error', 'Failed to submit review. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    if (checkingEligibility) {
        return (
            <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={Colors.PrimaryColor} />
                <Text style={styles.loadingText}>Checking review eligibility...</Text>
            </View>
        );
    }

    if (!canReview) {
        return (
            <View style={styles.loadingContainer}>
                <Icon name="close-circle-outline" size={64} color={Colors.Error} />
                <Text style={styles.errorText}>Unable to add review</Text>
            </View>
        );
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>

                <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
                    {/* Dock Info */}
                    <View style={styles.dockInfoContainer}>
                        {dockImage && (
                            <Image
                                source={{ uri: dockImage }}
                                style={styles.dockImage}
                                resizeMode="cover"
                            />
                        )}
                        <View style={styles.dockTextInfo}>
                            <Text style={styles.dockName}>{dockName}</Text>
                            <Text style={styles.reviewPrompt}>How was your experience?</Text>
                        </View>
                    </View>

                    {/* Rating Section */}
                    <View style={styles.ratingSection}>
                        <Text style={styles.sectionTitle}>Rating</Text>
                        <Text style={styles.sectionSubtitle}>Tap to rate your experience</Text>
                        {renderStars()}
                        {rating > 0 && (
                            <Text style={styles.ratingText}>
                                {rating} star{rating !== 1 ? 's' : ''} - {
                                    rating === 1 ? 'Poor' :
                                        rating === 2 ? 'Fair' :
                                            rating === 3 ? 'Good' :
                                                rating === 4 ? 'Very Good' : 'Excellent'
                                }
                            </Text>
                        )}
                    </View>

                    {/* Comment Section */}
                    <View style={styles.commentSection}>
                        <Text style={styles.sectionTitle}>Your Review</Text>
                        <Text style={styles.sectionSubtitle}>
                            Share your experience to help other boaters
                        </Text>
                        <TextInput
                            style={styles.commentInput}
                            placeholder="Write your review here..."
                            placeholderTextColor={Colors.Gray}
                            value={comment}
                            onChangeText={setComment}
                            multiline
                            numberOfLines={6}
                            textAlignVertical="top"
                            maxLength={500}
                        />
                        <Text style={styles.characterCount}>
                            {comment.length}/500 characters
                        </Text>
                    </View>

                    {/* Submit Button */}
                    <TouchableOpacity
                        style={[
                            styles.submitButton,
                            (rating === 0 || !comment.trim() || loading) && styles.submitButtonDisabled
                        ]}
                        onPress={handleSubmitReview}
                        disabled={rating === 0 || !comment.trim() || loading}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color={Colors.White} />
                        ) : (
                            <Text style={styles.submitButtonText}>Submit Review</Text>
                        )}
                    </TouchableOpacity>

                    <View style={styles.bottomSpacer} />
                </ScrollView>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: Colors.White,
    },
    container: {
        flex: 1,
        backgroundColor: Colors.White,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: Colors.White,
    },
    loadingText: {
        marginTop: rm(16),
        fontSize: rm(16),
        color: Colors.Gray,
    },
    errorText: {
        marginTop: rm(16),
        fontSize: rm(18),
        color: Colors.Error,
        textAlign: 'center',
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: rm(20),
        paddingTop: rm(50),
        paddingBottom: rm(15),
        backgroundColor: Colors.White,
        borderBottomWidth: 1,
        borderBottomColor: Colors.Border,
    },
    backButton: {
        width: rm(40),
        height: rm(40),
        borderRadius: rm(20),
        backgroundColor: Colors.LightGray,
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    headerSpacer: {
        width: rm(40),
    },
    scrollContainer: {
        flex: 1,
        paddingHorizontal: rm(20),
    },
    dockInfoContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.LightGray,
        borderRadius: rm(12),
        padding: rm(16),
        marginTop: rm(20),
    },
    dockImage: {
        width: rm(60),
        height: rm(60),
        borderRadius: rm(8),
        marginRight: rm(16),
    },
    dockTextInfo: {
        flex: 1,
    },
    dockName: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(4),
    },
    reviewPrompt: {
        fontSize: rm(14),
        color: Colors.Gray,
    },
    ratingSection: {
        marginTop: rm(32),
        alignItems: 'center',
    },
    sectionTitle: {
        fontSize: rm(20),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(4),
    },
    sectionSubtitle: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginBottom: rm(20),
        textAlign: 'center',
    },
    starsContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        marginBottom: rm(16),
    },
    starButton: {
        marginHorizontal: rm(4),
        padding: rm(4),
    },
    ratingText: {
        fontSize: rm(16),
        fontWeight: '600',
        color: Colors.PrimaryColor,
    },
    commentSection: {
        marginTop: rm(32),
    },
    commentInput: {
        borderWidth: 1,
        borderColor: Colors.Border,
        borderRadius: rm(12),
        padding: rm(16),
        fontSize: rm(16),
        color: Colors.Dark,
        backgroundColor: Colors.White,
        minHeight: rm(120),
    },
    characterCount: {
        fontSize: rm(12),
        color: Colors.Gray,
        textAlign: 'right',
        marginTop: rm(8),
    },
    submitButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(12),
        paddingVertical: rm(16),
        alignItems: 'center',
        marginTop: rm(32),
    },
    submitButtonDisabled: {
        backgroundColor: Colors.Gray,
        opacity: 0.6,
    },
    submitButtonText: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.White,
    },
    bottomSpacer: {
        height: rm(40),
    },
});

export default AddReview;
