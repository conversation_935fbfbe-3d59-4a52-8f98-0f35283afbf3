import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    SafeAreaView,
    TouchableOpacity,
    ScrollView,
    Alert,
    ActivityIndicator,
    Dimensions
} from 'react-native';
import { TextInput } from 'react-native-paper';
import Icon from 'react-native-vector-icons/Ionicons';
import { auth, db } from '@/config/firebase';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width } = Dimensions.get('window');

interface UserData {
    username: string;
    email: string;
    // contact: string;
    // fullName: string;
    role: string;
    phoneNumber: string;
    createdAt?: any;
}

function AccountDetail({ navigation }: RootScreenProps<Paths.AccountDetail>) {
    const [userData, setUserData] = useState<UserData>({
        username: '',
        email: '',
        // contact: '',
        // fullName: '',
        role: 'customer',
        phoneNumber: ''
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [isEditing, setIsEditing] = useState(false);

    useEffect(() => {
        fetchUserData();
    }, []);

    const fetchUserData = async () => {
        try {
            setLoading(true);
            const user = auth.currentUser;
            if (!user) {
                Alert.alert('Error', 'No user logged in');
                return;
            }

            // Get user role from AsyncStorage
            const userRole = await AsyncStorage.getItem('userRole') || 'customer';
            const myUser = await AsyncStorage.getItem('myUser');

            // Fetch user data from Firestore
            const userDoc = await getDoc(doc(db, 'users', user.uid));

            if (userDoc.exists()) {
                const data = userDoc.data();
                setUserData({
                    username: data.username || user.displayName || user.email?.split('@')[0] || '',
                    email: user.email || '',
                    // contact: data.contact || data.phone || '',
                    // fullName: data.fullName || data.name || user.displayName || '',
                    role: userRole,
                    createdAt: data.createdAt,
                    ...(myUser ? JSON.parse(myUser) : {})
                });
            } else {
                // If no Firestore document, use Firebase Auth data
                setUserData({
                    username: user.displayName || user.email?.split('@')[0] || '',
                    email: user.email || '',
                    // contact: '',
                    // fullName: user.displayName || '',
                    role: userRole,
                    phoneNumber: ''
                });
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            Alert.alert('Error', 'Failed to load user data');
        } finally {
            setLoading(false);
        }
    };

    const validateForm = () => {
        // if (!userData.fullName.trim()) {
        //     Alert.alert('Validation Error', 'Full name is required');
        //     return false;
        // }
        if (!userData.username.trim()) {
            Alert.alert('Validation Error', 'Username is required');
            return false;
        }
        if (userData.phoneNumber && !/^\+?[\d\s\-\(\)]+$/.test(userData.phoneNumber)) {
            Alert.alert('Validation Error', 'Please enter a valid phone number');
            return false;
        }
        return true;
    };

    const handleSave = async () => {
        if (!validateForm()) return;

        try {
            setSaving(true);
            const user = auth.currentUser;
            if (!user) return;

            // Update Firestore document
            await updateDoc(doc(db, 'users', user.uid), {
                username: userData.username.trim(),
                // contact: userData.contact.trim(),
                phoneNumber: userData.phoneNumber.trim(),
                // fullName: userData.fullName.trim(),
                updatedAt: new Date()
            });

            // Update AsyncStorage
            const updatedUserData = {
                ...userData,
                username: userData.username.trim(),
                // contact: userData.contact.trim(),
                phoneNumber: userData.phoneNumber.trim(),
                // fullName: userData.fullName.trim(),
            };
            await AsyncStorage.setItem('myUser', JSON.stringify(updatedUserData));

            setIsEditing(false);
            Alert.alert('Success', 'Account details updated successfully');
        } catch (error) {
            console.error('Error updating user data:', error);
            Alert.alert('Error', 'Failed to update account details. Please try again.');
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (field: keyof UserData, value: string) => {
        setUserData(prev => ({ ...prev, [field]: value }));
    };

    if (loading) {
        return (
            <SafeAreaView style={styles.safeArea}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#3674B5" />
                    <Text style={styles.loadingText}>Loading account details...</Text>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                {/* Profile Header */}
                <View style={styles.profileHeader}>
                    <View style={styles.avatarContainer}>
                        <View style={styles.avatar}>
                            <Icon name="person" size={50} color="#3674B5" />
                        </View>
                        <View style={styles.roleIndicator}>
                            <Icon
                                name={userData.role === 'owner' ? 'business' : 'person'}
                                size={16}
                                color="white"
                            />
                        </View>
                    </View>
                    <Text style={styles.profileName}>{userData.username}</Text>
                    <View style={styles.roleChip}>
                        <Text style={styles.roleText}>
                            {userData.role === 'owner' ? 'Dock Owner' : 'Customer'}
                        </Text>
                    </View>
                </View>

                {/* Account Details Form */}
                <View style={styles.formContainer}>
                    <View style={styles.formHeader}>
                        <Text style={styles.formTitle}>Account Information</Text>
                        <TouchableOpacity
                            style={styles.editButton}
                            onPress={() => isEditing ? handleSave() : setIsEditing(true)}
                            disabled={saving}
                        >
                            {saving ? (
                                <ActivityIndicator size="small" color="#3674B5" />
                            ) : (
                                <Icon
                                    name={isEditing ? 'checkmark' : 'pencil'}
                                    size={20}
                                    color="#3674B5"
                                />
                            )}
                        </TouchableOpacity>
                    </View>

                    {/* <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Full Name</Text>
                        <TextInput
                            value={userData.fullName}
                            onChangeText={(text) => handleInputChange('fullName', text)}
                            style={styles.textInput}
                            mode="outlined"
                            editable={isEditing}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="Enter your full name"
                        />
                    </View> */}

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Username</Text>
                        <TextInput
                            value={userData.username}
                            onChangeText={(text) => handleInputChange('username', text)}
                            style={styles.textInput}
                            mode="outlined"
                            editable={isEditing}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                        />
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Email</Text>
                        <TextInput
                            value={userData.email}
                            style={styles.textInput}
                            mode="outlined"
                            editable={false}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={[styles.inputContent, styles.disabledInput]}
                            right={<TextInput.Icon icon="lock" color="#6C757D" />}
                        />
                        <Text style={styles.helperText}>Email cannot be changed</Text>
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Contact Number</Text>
                        <TextInput
                            value={userData.phoneNumber}
                            onChangeText={(text) => handleInputChange('phoneNumber', text)}
                            style={styles.textInput}
                            mode="outlined"
                            editable={isEditing}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            keyboardType="phone-pad"
                            placeholder="Enter your phone number"
                        />
                    </View>
                    {isEditing && (
                        <View style={styles.bottomContainer}>
                            <View style={styles.editingActions}>
                                <TouchableOpacity
                                    style={styles.cancelButton}
                                    onPress={() => {
                                        setIsEditing(false);
                                        fetchUserData(); // Reset to original data
                                    }}
                                >
                                    <Icon name="close" size={20} color="#6C757D" />
                                    <Text style={styles.cancelButtonText}>Cancel</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    style={[styles.updateButton, saving && styles.savingButton]}
                                    onPress={handleSave}
                                    disabled={saving}
                                >
                                    {saving ? (
                                        <ActivityIndicator size="small" color="white" />
                                    ) : (
                                        <Icon name="checkmark" size={20} color="white" />
                                    )}
                                    <Text style={styles.updateButtonText}>
                                        {saving ? 'Updating...' : 'Update'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    )}
                </View>


            </ScrollView>

            {/* Bottom Update Button - Only show when editing */}

        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    container: {
        flex: 1,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 20,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
    },
    profileHeader: {
        backgroundColor: 'white',
        paddingHorizontal: 24,
        paddingVertical: 32,
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#E9ECEF',
        marginBottom: 24,
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: 16,
    },
    avatar: {
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 4,
        borderColor: '#3674B5',
    },
    roleIndicator: {
        position: 'absolute',
        bottom: -2,
        right: -2,
        width: 32,
        height: 32,
        borderRadius: 16,
        backgroundColor: '#3674B5',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: 3,
        borderColor: 'white',
    },
    profileName: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 8,
        textAlign: 'center',
    },
    roleChip: {
        backgroundColor: '#3674B5',
        paddingHorizontal: 16,
        paddingVertical: 6,
        borderRadius: 20,
    },
    roleText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '600',
    },
    formContainer: {
        backgroundColor: 'white',
        marginHorizontal: 20,
        borderRadius: 16,
        padding: 24,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
        marginBottom: 24,
    },
    formHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 24,
    },
    formTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
    },
    editButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
    },
    inputGroup: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 8,
    },
    textInput: {
        backgroundColor: 'white',
        fontSize: 16,
    },
    inputContent: {
        fontSize: 16,
        color: '#2C3E50',
    },
    disabledInput: {
        color: '#6C757D',
    },
    helperText: {
        fontSize: 12,
        color: '#6C757D',
        marginTop: 4,
        fontStyle: 'italic',
    },
    statsContainer: {
        backgroundColor: 'white',
        marginHorizontal: 20,
        borderRadius: 16,
        padding: 24,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
        marginBottom: 20, // Normal spacing when not editing
    },
    statsTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 20,
    },
    statsGrid: {
        flexDirection: 'row',
        justifyContent: 'space-between',
    },
    statItem: {
        flex: 1,
        alignItems: 'center',
        paddingHorizontal: 8,
    },
    statLabel: {
        fontSize: 12,
        color: '#6C757D',
        marginTop: 8,
        marginBottom: 4,
        textAlign: 'center',
    },
    statValue: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        textAlign: 'center',
    },
    verifiedText: {
        color: '#28A745',
    },
    bottomContainer: {
        // paddingHorizontal: 20,
        // paddingVertical: 16,
        // paddingBottom: 34, // Safe area padding
        borderTopColor: '#E9ECEF',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: -2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 8,
    },
    updateButton: {
        flex: 2,
        backgroundColor: '#28A745',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        shadowColor: '#28A745',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
        margin: 5
    },
    updateButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'white',
        marginLeft: 6,
    },
    editingActions: {
        flexDirection: 'row',
        // gap: 12,
    },
    cancelButton: {
        flex: 1,
        backgroundColor: '#F8F9FA',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        borderWidth: 1,
        borderColor: '#DEE2E6',
        margin: 5
    },
    cancelButtonText: {
        fontSize: 16,
        fontWeight: '600',
        color: '#6C757D',
        marginLeft: 6,
    },
    savingButton: {
        backgroundColor: '#6C757D',
    },
});

export default AccountDetail;
