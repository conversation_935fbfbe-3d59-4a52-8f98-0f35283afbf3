import React, { useState } from 'react';
import {
    ScrollView,
    Text,
    TouchableOpacity,
    View,
    Alert,
    ActivityIndicator,
    StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TextInput } from 'react-native-paper';
import Icon from 'react-native-vector-icons/Ionicons';
import { Paths } from '@/navigation/paths';
import type { RootScreenProps } from '@/navigation/types';
import { auth, db } from '@/config/firebase';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { doc, setDoc } from 'firebase/firestore';
import { storage } from '@/App';
import { SafeScreen } from '@/components/templates';

import { rm } from '@/utils/scaling';

function Register({ navigation }: RootScreenProps<Paths.Register>) {
    // State variables for all input fields
    const [email, setEmail] = useState('');
    const [firstName, setFirstName] = useState('');
    const [lastName, setLastName] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [phoneNumber, setPhoneNumber] = useState('');
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    // Password validation functions (same as ChangePassword screen)
    const getPasswordRequirements = (password: string) => {
        return {
            minLength: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumbers: /\d/.test(password),
            hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        };
    };

    const validatePassword = (password: string) => {
        const requirements = getPasswordRequirements(password);

        if (!requirements.minLength) {
            return 'Password must be at least 8 characters long';
        }
        if (!requirements.hasUpperCase) {
            return 'Password must contain at least one uppercase letter';
        }
        if (!requirements.hasLowerCase) {
            return 'Password must contain at least one lowercase letter';
        }
        if (!requirements.hasNumbers) {
            return 'Password must contain at least one number';
        }
        if (!requirements.hasSpecialChar) {
            return 'Password must contain at least one special character';
        }
        return null;
    };

    const passwordRequirements = getPasswordRequirements(password);

    const getPasswordStrength = () => {
        const requirements = passwordRequirements;
        const metCount = Object.values(requirements).filter(Boolean).length;

        if (metCount === 0) return { strength: 'none', color: '#DEE2E6', text: '' };
        if (metCount <= 2) return { strength: 'weak', color: '#DC3545', text: 'Weak' };
        if (metCount <= 3) return { strength: 'fair', color: '#FFC107', text: 'Fair' };
        if (metCount <= 4) return { strength: 'good', color: '#17A2B8', text: 'Good' };
        return { strength: 'strong', color: '#28A745', text: 'Strong' };
    };

    const passwordStrength = getPasswordStrength();

    const handleRegister = async () => {
        // Enhanced Input Validation
        if (!firstName.trim()) {
            Alert.alert('Validation Error', 'Please enter your first name');
            return;
        }

        if (!lastName.trim()) {
            Alert.alert('Validation Error', 'Please enter your last name');
            return;
        }

        if (!email.trim()) {
            Alert.alert('Validation Error', 'Please enter your email address');
            return;
        }

        // Email format validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            Alert.alert('Validation Error', 'Please enter a valid email address');
            return;
        }

        if (!phoneNumber.trim()) {
            Alert.alert('Validation Error', 'Please enter your phone number');
            return;
        }

        if (!password.trim()) {
            Alert.alert('Validation Error', 'Please enter a password');
            return;
        }

        // Password strength validation
        const passwordValidation = validatePassword(password);
        if (passwordValidation) {
            Alert.alert('Password Requirements', passwordValidation);
            return;
        }

        if (!confirmPassword.trim()) {
            Alert.alert('Validation Error', 'Please confirm your password');
            return;
        }

        if (password !== confirmPassword) {
            Alert.alert('Validation Error', 'Passwords do not match');
            return;
        }

        setLoading(true);

        try {
            // 1. Create user with email and password using Firebase Authentication
            // Use the 'auth' instance imported from your config file
            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user; // Get the newly created user object


            if (user) {
                await updateProfile(user, {
                    displayName: firstName + ' ' + lastName,

                })
            }
            // 2. Store additional user details in Firestore
            // The 'users' collection will be created automatically if it doesn't exist
            // The document ID is set to user.uid to link it with the Auth record
            if (user) {
                await setDoc(doc(db, 'users', user.uid), {
                    uid: user.uid, // Store the user's unique ID
                    email: user.email,
                    firstName: firstName,
                    lastName: lastName,
                    phoneNumber: phoneNumber,
                    // displayName: user.displayName, // Optional: store display name
                    createdAt: new Date(), // Optional: store creation timestamp

                });
                try {
                    const userDataToSave = {
                        uid: user.uid,
                        email: user.email,
                        displayName: firstName + ' ' + lastName,
                        phoneNumber: phoneNumber,
                        firstName: firstName,
                        lastName: lastName,
                        createdAt: new Date().toISOString(),
                    };
                    storage.set('currentUser', JSON.stringify(userDataToSave));
                } catch (mmkvError) {
                    console.error('Failed to save user data to MMKV:', mmkvError);
                }
                console.log('User details stored in Firestore.');
            }

            // Success feedback and navigation
            // navigation.navigate(Paths.Login); // Navigate to login screen

        } catch (error: any) {
            console.error('Registration Error:', error); // Log the full error for debugging

            let errorMessage = 'An unexpected error occurred during registration. Please try again.';
            // Handle specific Firebase Auth error codes
            if (error.code) {
                switch (error.code) {
                    case 'auth/email-already-in-use':
                        errorMessage = 'That email address is already in use!';
                        break;
                    case 'auth/invalid-email':
                        errorMessage = 'The email address is not valid.';
                        break;
                    case 'auth/weak-password':
                        errorMessage = 'Password should be at least 6 characters.';
                        break;
                    // You can add more specific error codes if you encounter them
                    default:
                        errorMessage = error.message; // Use Firebase's error message if not specific
                }
            }
            Alert.alert('Registration Error', errorMessage);

        } finally {
            setLoading(false); // Stop loading indicator regardless of success or failure
        }
    };

    return (
        // <SafeScreen >
        <ScrollView
            style={styles.container}
            contentContainerStyle={styles.contentContainer}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
        >
            {/* Header Section */}
            <View style={styles.headerContainer}>
                <View style={styles.iconContainer}>
                    <Icon name="person-add" size={40} color="#3674B5" />
                </View>
                <Text style={styles.headerTitle}>Create Account</Text>
                <Text style={styles.headerSubtitle}>
                    Join us today and start your journey with our amazing platform
                </Text>
            </View>

            {/* Form Container */}
            <View style={styles.formContainer}>
                {/* Name Fields Row */}
                <View style={styles.nameRow}>
                    <View style={styles.nameField}>
                        <Text style={styles.inputLabel}>First Name</Text>
                        <TextInput
                            value={firstName}
                            onChangeText={setFirstName}
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="First Name"
                            autoCapitalize="words"
                        />
                    </View>
                    <View style={styles.nameField}>
                        <Text style={styles.inputLabel}>Last Name</Text>
                        <TextInput
                            value={lastName}
                            onChangeText={setLastName}
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="Last Name"
                            autoCapitalize="words"
                        />
                    </View>
                </View>

                {/* Email Field */}
                <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Email Address</Text>
                    <TextInput
                        value={email}
                        onChangeText={setEmail}
                        style={styles.textInput}
                        mode="outlined"
                        activeOutlineColor="#3674B5"
                        outlineColor="#E9ECEF"
                        contentStyle={styles.inputContent}
                        placeholder="Enter your email address"
                        keyboardType="email-address"
                        autoCapitalize="none"
                    />
                </View>

                {/* Phone Number Field */}
                <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Phone Number</Text>
                    <TextInput
                        value={phoneNumber}
                        onChangeText={setPhoneNumber}
                        style={styles.textInput}
                        mode="outlined"
                        activeOutlineColor="#3674B5"
                        outlineColor="#E9ECEF"
                        contentStyle={styles.inputContent}
                        placeholder="Enter your phone number"
                        keyboardType="phone-pad"
                    />
                </View>
                {/* Password Field */}
                <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Password</Text>
                    <View style={styles.passwordInputContainer}>
                        <TextInput
                            value={password}
                            onChangeText={setPassword}
                            style={styles.textInput}
                            mode="outlined"
                            secureTextEntry={!showPassword}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="Enter your password"
                            autoCapitalize="none"
                        />
                        <TouchableOpacity
                            style={styles.eyeButton}
                            onPress={() => setShowPassword(!showPassword)}
                        >
                            <Icon
                                name={showPassword ? 'eye-off' : 'eye'}
                                size={20}
                                color="#6C757D"
                            />
                        </TouchableOpacity>
                    </View>

                    {/* Password Strength Indicator */}
                    {password.length > 0 && (
                        <View style={styles.strengthContainer}>
                            <View style={styles.strengthBar}>
                                <View
                                    style={[
                                        styles.strengthFill,
                                        {
                                            width: `${(Object.values(passwordRequirements).filter(Boolean).length / 5) * 100}%`,
                                            backgroundColor: passwordStrength.color
                                        }
                                    ]}
                                />
                            </View>
                            {passwordStrength.text && (
                                <Text style={[styles.strengthText, { color: passwordStrength.color }]}>
                                    {passwordStrength.text}
                                </Text>
                            )}
                        </View>
                    )}
                </View>

                {/* Confirm Password Field */}
                <View style={styles.inputGroup}>
                    <Text style={styles.inputLabel}>Confirm Password</Text>
                    <View style={styles.passwordInputContainer}>
                        <TextInput
                            value={confirmPassword}
                            onChangeText={setConfirmPassword}
                            style={styles.textInput}
                            mode="outlined"
                            secureTextEntry={!showConfirmPassword}
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="Confirm your password"
                            autoCapitalize="none"
                        />
                        <TouchableOpacity
                            style={styles.eyeButton}
                            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                            <Icon
                                name={showConfirmPassword ? 'eye-off' : 'eye'}
                                size={20}
                                color="#6C757D"
                            />
                        </TouchableOpacity>
                    </View>
                </View>
                {/* Password Requirements */}
                <View style={styles.requirementsContainer}>
                    <Text style={styles.requirementsTitle}>Password Requirements:</Text>
                    <View style={styles.requirementsList}>
                        <View style={styles.requirementItem}>
                            <Icon
                                name={passwordRequirements.minLength ? "checkmark-circle" : "ellipse-outline"}
                                size={16}
                                color={passwordRequirements.minLength ? "#28A745" : "#DEE2E6"}
                            />
                            <Text style={[
                                styles.requirementText,
                                passwordRequirements.minLength && styles.requirementTextMet
                            ]}>
                                At least 8 characters long
                            </Text>
                        </View>
                        <View style={styles.requirementItem}>
                            <Icon
                                name={passwordRequirements.hasUpperCase ? "checkmark-circle" : "ellipse-outline"}
                                size={16}
                                color={passwordRequirements.hasUpperCase ? "#28A745" : "#DEE2E6"}
                            />
                            <Text style={[
                                styles.requirementText,
                                passwordRequirements.hasUpperCase && styles.requirementTextMet
                            ]}>
                                Contains uppercase letter (A-Z)
                            </Text>
                        </View>
                        <View style={styles.requirementItem}>
                            <Icon
                                name={passwordRequirements.hasLowerCase ? "checkmark-circle" : "ellipse-outline"}
                                size={16}
                                color={passwordRequirements.hasLowerCase ? "#28A745" : "#DEE2E6"}
                            />
                            <Text style={[
                                styles.requirementText,
                                passwordRequirements.hasLowerCase && styles.requirementTextMet
                            ]}>
                                Contains lowercase letter (a-z)
                            </Text>
                        </View>
                        <View style={styles.requirementItem}>
                            <Icon
                                name={passwordRequirements.hasNumbers ? "checkmark-circle" : "ellipse-outline"}
                                size={16}
                                color={passwordRequirements.hasNumbers ? "#28A745" : "#DEE2E6"}
                            />
                            <Text style={[
                                styles.requirementText,
                                passwordRequirements.hasNumbers && styles.requirementTextMet
                            ]}>
                                Contains at least one number (0-9)
                            </Text>
                        </View>
                        <View style={styles.requirementItem}>
                            <Icon
                                name={passwordRequirements.hasSpecialChar ? "checkmark-circle" : "ellipse-outline"}
                                size={16}
                                color={passwordRequirements.hasSpecialChar ? "#28A745" : "#DEE2E6"}
                            />
                            <Text style={[
                                styles.requirementText,
                                passwordRequirements.hasSpecialChar && styles.requirementTextMet
                            ]}>
                                Contains special character (!@#$%^&*)
                            </Text>
                        </View>
                    </View>
                </View>
            </View>

            {/* Register Button */}
            <TouchableOpacity
                style={[styles.registerButton, loading && styles.registerButtonDisabled]}
                onPress={handleRegister}
                disabled={loading}
                activeOpacity={0.8}
            >
                {loading ? (
                    <ActivityIndicator size="small" color="white" />
                ) : (
                    <Icon name="person-add" size={20} color="white" />
                )}
                <Text style={styles.registerButtonText}>
                    {loading ? 'Creating Account...' : 'Create Account'}
                </Text>
            </TouchableOpacity>

            {/* Login Link */}
            <View style={styles.loginContainer}>
                <Text style={styles.loginText}>Already have an account? </Text>
                <TouchableOpacity onPress={() => navigation.navigate(Paths.Login)}>
                    <Text style={styles.loginLink}>Sign In</Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
        // </SafeScreen>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        // backgroundColor: '#F8F9FA',
        top: 0
    },
    container: {
        flex: 1,
        paddingVertical: rm(20)
    },
    contentContainer: {
        paddingBottom: rm(20),
    },
    headerContainer: {
        alignItems: 'center',
        // paddingVertical: rm(40),
        paddingHorizontal: rm(20),
        paddingBottom: rm(40)
    },
    iconContainer: {
        width: rm(80),
        height: rm(80),
        borderRadius: rm(40),
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: rm(20),
    },
    headerTitle: {
        fontSize: rm(28),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(8),
        textAlign: 'center',
    },
    headerSubtitle: {
        fontSize: rm(16),
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: rm(22),
    },
    formContainer: {
        backgroundColor: 'white',
        borderRadius: rm(16),
        padding: rm(24),
        marginHorizontal: rm(20),
        marginBottom: rm(20),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    nameRow: {
        flexDirection: 'row',
        gap: rm(12),
        marginBottom: rm(20),
    },
    nameField: {
        flex: 1,
    },
    inputGroup: {
        marginBottom: rm(20),
    },
    inputLabel: {
        fontSize: rm(16),
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: rm(8),
    },
    passwordInputContainer: {
        position: 'relative',
    },
    textInput: {
        backgroundColor: 'white',
        fontSize: rm(14),
    },
    inputContent: {
        fontSize: rm(16),
        color: '#2C3E50',
        paddingRight: rm(40), // Space for eye button
    },
    eyeButton: {
        position: 'absolute',
        right: rm(12),
        top: rm(16),
        padding: rm(8),
    },
    strengthContainer: {
        marginTop: rm(12),
        flexDirection: 'row',
        alignItems: 'center',
        gap: rm(12),
    },
    strengthBar: {
        flex: 1,
        height: rm(4),
        backgroundColor: '#E9ECEF',
        borderRadius: rm(2),
        overflow: 'hidden',
    },
    strengthFill: {
        height: '100%',
        borderRadius: rm(2),
    },
    strengthText: {
        fontSize: rm(12),
        fontWeight: '600',
        minWidth: rm(50),
        textAlign: 'right',
    },
    requirementsContainer: {
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
        marginTop: rm(8),
        borderLeftWidth: 4,
        borderLeftColor: '#3674B5',
    },
    requirementsTitle: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: rm(12),
    },
    requirementsList: {
        gap: rm(8),
    },
    requirementItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: rm(8),
    },
    requirementText: {
        fontSize: rm(14),
        color: '#6C757D',
        flex: 1,
    },
    requirementTextMet: {
        color: '#28A745',
        fontWeight: '500',
    },
    registerButton: {
        backgroundColor: '#3674B5',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: rm(16),
        borderRadius: rm(12),
        marginHorizontal: rm(20),
        marginBottom: rm(20),
        shadowColor: '#3674B5',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
        gap: rm(8),
    },
    registerButtonDisabled: {
        backgroundColor: '#6C757D',
        shadowOpacity: 0.1,
    },
    registerButtonText: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: 'white',
    },
    loginContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: rm(20),
        marginBottom: rm(20),
    },
    loginText: {
        fontSize: rm(14),
        color: '#6C757D',
    },
    loginLink: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#3674B5',
    },
});

export default Register;