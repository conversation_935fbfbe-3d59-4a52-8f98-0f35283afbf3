import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    ScrollView,
    Dimensions,
    ActivityIndicator,
    RefreshControl,
    TouchableOpacity,
    Alert,
    Linking,
    FlatList,
} from 'react-native';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import type { TabScreenProps } from '@/navigation/types';
import { Paths } from '@/navigation/paths';
import Icon from 'react-native-vector-icons/Ionicons';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth } from '@/config/firebase';
import {
    getCustomerBookings,
    getOwnerDockBookings,
    BookingDisplay,
} from '@/utils/bookingUtils';
import BookingCard from '@/components/BookingCard/BookingCard';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
const guidelineBaseWidth = 350;
const scale = (size: number) => (screenWidth / guidelineBaseWidth) * size;
const moderateScale = (size: number, factor = 0.5) => size + (scale(size) - size) * factor;
const rm = moderateScale;

const Colors = {
    PrimaryColor: '#3674B5',
    AccentColor: '#ffc107',
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Border: '#dee2e6',
    Success: '#28a745',
    Warning: '#ffc107',
    Error: '#dc3545',
};

function Booking({ navigation: tabNavigation }: TabScreenProps<Paths.Booking>) {
    const navigation = useNavigation(); // Get parent navigation for stack navigation
    const [userRole, setUserRole] = useState<string | null>(null);
    const [bookings, setBookings] = useState<BookingDisplay[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [refreshing, setRefreshing] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);

    // Load user role and fetch bookings
    // Note: This respects the current profile role, not the user's inherent ownership
    // If an owner switches to customer profile, they see customer bookings
    const loadUserRole = async () => {
        try {
            const storedRole = await AsyncStorage.getItem('userRole');
            setUserRole(storedRole);
            return storedRole;
        } catch (error) {
            console.error('Error loading user role:', String(error));
            return null;
        }
    };

    const fetchBookings = async (role: string | null = null) => {
        if (!auth.currentUser) {
            setError('User not authenticated');
            setLoading(false);
            return;
        }

        try {
            setError(null);
            const currentRole = role || userRole;

            // Always respect the current profile role
            // If user is in customer profile, show customer bookings
            // If user is in owner profile, show dock bookings
            if (currentRole === 'owner') {
                console.log('Fetching owner dock bookings for current profile...');
                const ownerBookings = await getOwnerDockBookings(auth.currentUser.uid);
                setBookings(ownerBookings);
            } else {
                console.log('Fetching customer bookings for current profile...');
                const customerBookings = await getCustomerBookings(auth.currentUser.uid);
                setBookings(customerBookings);
            }
        } catch (error) {
            console.error('Error fetching bookings:', String(error));
            setError('Failed to load bookings');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    // Load data on component mount
    useEffect(() => {
        const initializeData = async () => {
            const role = await loadUserRole();
            await fetchBookings(role);
        };
        initializeData();
    }, []);

    // Refresh data when screen comes into focus or role changes
    useFocusEffect(
        React.useCallback(() => {
            const refreshData = async () => {
                try {
                    console.log('Booking screen focused, checking for role changes...');
                    const currentRole = await AsyncStorage.getItem('userRole');

                    // If role changed, update state and fetch new data
                    if (currentRole !== userRole) {
                        setUserRole(currentRole);
                        await fetchBookings(currentRole);
                    } else if (userRole) {
                        // Same role, just refresh data
                        await fetchBookings();
                    }
                } catch (error) {
                    console.error('Error in focus effect:', String(error));
                }
            };

            refreshData();
        }, [userRole])
    );

    const handleRefresh = () => {
        setRefreshing(true);
        fetchBookings();
    };

    const handleBookingPress = (booking: BookingDisplay) => {
        // Navigate to dock details using parent navigation
        (navigation as any).navigate(Paths.ViewDock, { dockId: booking.dockId });
    };

    const handleContactPress = (booking: BookingDisplay) => {
        Alert.alert(
            'Contact Customer',
            `Call ${booking.customerName}?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Call',
                    onPress: () => {
                        const phoneUrl = `tel:${booking.customerContact}`;
                        Linking.openURL(phoneUrl).catch((error) => {
                            console.error('Phone call error:', String(error));
                            Alert.alert('Error', 'Unable to make phone call');
                        });
                    },
                },
            ]
        );
    };

    const renderEmptyState = () => {
        const isOwnerProfile = userRole === 'owner';
        return (
            <View style={styles.emptyContainer}>
                <Icon
                    name={isOwnerProfile ? 'business-outline' : 'calendar-outline'}
                    size={80}
                    color={Colors.Gray}
                    style={{ marginBottom: rm(24) }}
                />
                <Text style={styles.emptyTitle}>
                    {isOwnerProfile ? 'No Dock Bookings Yet' : 'No Bookings Yet'}
                </Text>
                <Text style={styles.emptySubtitle}>
                    {isOwnerProfile
                        ? 'When customers book your docks, they will appear here.'
                        : 'Your booking history will appear here once you make your first reservation.'}
                </Text>
                {!isOwnerProfile && (
                    <TouchableOpacity
                        style={styles.exploreButton}
                        onPress={() => tabNavigation.navigate(Paths.Dashboard)}
                    >
                        <Text style={styles.exploreButtonText}>Explore Docks</Text>
                    </TouchableOpacity>
                )}
            </View>
        );
    };

    const renderErrorState = () => (
        <View style={styles.errorContainer}>
            <Icon
                name="alert-circle-outline"
                size={80}
                color={Colors.Error}
                style={{ marginBottom: rm(24) }}
            />
            <Text style={styles.errorTitle}>Unable to Load Bookings</Text>
            <Text style={styles.errorSubtitle}>{error}</Text>
            <TouchableOpacity style={styles.retryButton} onPress={() => fetchBookings()}>
                <Text style={styles.retryButtonText}>Try Again</Text>
            </TouchableOpacity>
        </View>
    );

    const renderHeader = () => {
        const isOwnerProfile = userRole === 'owner';
        const totalBookings = bookings.length;
        const activeBookings = bookings.filter(b =>
            b.status.toLowerCase() === 'approved' || b.status.toLowerCase() === 'pending'
        ).length;

        return (
            <View style={styles.header}>
                <View style={styles.headerContent}>
                    <Text style={styles.headerTitle}>
                        {isOwnerProfile ? 'Dock Bookings' : 'My Bookings'}
                    </Text>
                    <Text style={styles.headerSubtitle}>
                        {isOwnerProfile ? 'Bookings for your docks' : 'Your booking history'} • {totalBookings} total • {activeBookings} active
                    </Text>
                </View>
                {/* <TouchableOpacity style={styles.filterButton}>
                    <Icon name="filter-outline" size={20} color={Colors.PrimaryColor} />
                </TouchableOpacity> */}
            </View>
        );
    };

    if (loading) {
        return (
            <SafeScreen style={styles.safeArea}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={Colors.PrimaryColor} />
                    <Text style={styles.loadingText}>Loading bookings...</Text>
                </View>
            </SafeScreen>
        );
    }

    if (error && bookings.length === 0) {
        return (
            <SafeScreen>
                {renderErrorState()}
            </SafeScreen>
        );
    }

    return (
        // <SafeScreen>
        <View style={styles.container}>
            {renderHeader()}

            {bookings.length === 0 ? (
                <ScrollView
                    contentContainerStyle={styles.scrollContent}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={handleRefresh}
                            colors={[Colors.PrimaryColor]}
                        />
                    }
                >
                    {renderEmptyState()}
                </ScrollView>
            ) : (
                <FlatList
                    data={bookings}
                    keyExtractor={(item) => item.id}
                    renderItem={({ item }) => (
                        <BookingCard
                            booking={item}
                            isOwnerView={userRole === 'owner'}
                            onPress={() => handleBookingPress(item)}
                            onContactPress={() => handleContactPress(item)}
                        />
                    )}
                    contentContainerStyle={styles.listContent}
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={handleRefresh}
                            colors={[Colors.PrimaryColor]}
                        />
                    }
                />
            )}
        </View>
        // </SafeScreen>
    );
}

const styles = {
    safeArea: {
        flex: 1,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        backgroundColor: Colors.LightGray,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        backgroundColor: Colors.White,
    },
    loadingText: {
        marginTop: rm(16),
        fontSize: rm(16),
        color: Colors.Gray,
    },
    scrollContent: {
        flexGrow: 1,
    },
    listContent: {
        paddingBottom: rm(20),
    },
    header: {
        flexDirection: 'row' as const,
        justifyContent: 'space-between' as const,
        alignItems: 'center' as const,
        paddingHorizontal: rm(20),
        paddingVertical: rm(16),
        backgroundColor: Colors.White,
        borderBottomWidth: 1,
        borderBottomColor: Colors.Border,
    },
    headerContent: {
        flex: 1,
    },
    headerTitle: {
        fontSize: rm(24),
        fontWeight: 'bold' as const,
        color: Colors.Heading,
        marginBottom: rm(4),
    },
    headerSubtitle: {
        fontSize: rm(14),
        color: Colors.Gray,
    },
    filterButton: {
        width: rm(40),
        height: rm(40),
        borderRadius: rm(20),
        backgroundColor: Colors.LightGray,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        paddingHorizontal: rm(40),
        minHeight: screenHeight * 0.6,
    },
    emptyTitle: {
        fontSize: rm(24),
        fontWeight: 'bold' as const,
        color: Colors.Heading,
        marginBottom: rm(12),
        textAlign: 'center' as const,
    },
    emptySubtitle: {
        fontSize: rm(16),
        color: Colors.Gray,
        textAlign: 'center' as const,
        lineHeight: rm(24),
        marginBottom: rm(32),
    },
    exploreButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(25),
        paddingHorizontal: rm(32),
        paddingVertical: rm(12),
    },
    exploreButtonText: {
        fontSize: rm(16),
        fontWeight: 'bold' as const,
        color: Colors.White,
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center' as const,
        alignItems: 'center' as const,
        paddingHorizontal: rm(40),
        backgroundColor: Colors.White,
    },
    errorTitle: {
        fontSize: rm(24),
        fontWeight: 'bold' as const,
        color: Colors.Error,
        marginBottom: rm(12),
        textAlign: 'center' as const,
    },
    errorSubtitle: {
        fontSize: rm(16),
        color: Colors.Gray,
        textAlign: 'center' as const,
        lineHeight: rm(24),
        marginBottom: rm(32),
    },
    retryButton: {
        backgroundColor: Colors.Error,
        borderRadius: rm(25),
        paddingHorizontal: rm(32),
        paddingVertical: rm(12),
    },
    retryButtonText: {
        fontSize: rm(16),
        fontWeight: 'bold' as const,
        color: Colors.White,
    },
};

export default Booking;
