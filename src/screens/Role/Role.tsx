
import { ScrollView, Text, TouchableOpacity, View } from 'react-native';
import { Paths } from '@/navigation/paths';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native-paper';
import type { RootScreenProps } from '@/navigation/types';

function Role({ navigation }: RootScreenProps<Paths.Role>) {
    const { layout, backgrounds } = useTheme();
    const { t } = useTranslation();

    return (
        <SafeScreen>
            <ScrollView
                bounces={false}
                keyboardShouldPersistTaps="handled"
            >
                <View style={[
                    layout.flex_1,
                    layout.col,
                    layout.itemsCenter,
                    layout.justifyCenter,
                    backgrounds.white

                ]}>
                    <AssetByVariant path="Logo" resizeMode="contain" style={{ marginBottom: 30, alignSelf: "center" }} />

                    <Text style={{ marginHorizontal: 40, fontSize: 16, color: "#838383", fontWeight: '600', marginVertical: 10 }}>Choose Your Role</Text>
                    <TouchableOpacity onPress={() => navigation.navigate(Paths.OwnerDashboard)} style={{ marginVertical: 10, marginHorizontal: 40, borderRadius: 8, height: 100, justifyContent: 'center', alignItems: 'center', borderWidth: 4, borderColor: "#3674B5" }}>
                        <View style={{ alignItems: 'center', justifyContent: 'center', }}>
                            <Text style={{ color: "#3674B5", fontWeight: '600', fontSize: 24 }}>Dock Owner</Text>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => { }} style={{ marginVertical: 10, marginHorizontal: 40, borderRadius: 8, height: 100, justifyContent: 'center', alignItems: 'center', borderWidth: 4, borderColor: "#3674B5" }}>
                        <View style={{ alignItems: 'center', justifyContent: 'center', }}>
                            <Text style={{ color: "#3674B5", fontWeight: '600', fontSize: 24 }}>Customer (Renter)</Text>
                        </View>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </SafeScreen>
    );
}

export default Role;