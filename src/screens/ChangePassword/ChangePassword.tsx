import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from 'react-native';
import { TextInput } from 'react-native-paper';
import { getAuth, updatePassword, EmailAuthProvider, reauthenticateWithCredential } from 'firebase/auth';
import Icon from 'react-native-vector-icons/Ionicons';
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import { rm } from '@/utils/scaling';

function ChangePassword({ navigation }: RootScreenProps<Paths.ChangePassword>) {
    const [oldPassword, setOldPassword] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [showOldPassword, setShowOldPassword] = useState(false);
    const [showNewPassword, setShowNewPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const getPasswordRequirements = (password: string) => {
        return {
            minLength: password.length >= 8,
            hasUpperCase: /[A-Z]/.test(password),
            hasLowerCase: /[a-z]/.test(password),
            hasNumbers: /\d/.test(password),
            hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
        };
    };

    const validatePassword = (password: string) => {
        const requirements = getPasswordRequirements(password);

        if (!requirements.minLength) {
            return 'Password must be at least 8 characters long';
        }
        if (!requirements.hasUpperCase) {
            return 'Password must contain at least one uppercase letter';
        }
        if (!requirements.hasLowerCase) {
            return 'Password must contain at least one lowercase letter';
        }
        if (!requirements.hasNumbers) {
            return 'Password must contain at least one number';
        }
        if (!requirements.hasSpecialChar) {
            return 'Password must contain at least one special character';
        }
        return null;
    };

    const passwordRequirements = getPasswordRequirements(newPassword);

    const getPasswordStrength = () => {
        const requirements = passwordRequirements;
        const metCount = Object.values(requirements).filter(Boolean).length;

        if (metCount === 0) return { strength: 'none', color: '#DEE2E6', text: '' };
        if (metCount <= 2) return { strength: 'weak', color: '#DC3545', text: 'Weak' };
        if (metCount <= 3) return { strength: 'fair', color: '#FFC107', text: 'Fair' };
        if (metCount <= 4) return { strength: 'good', color: '#17A2B8', text: 'Good' };
        return { strength: 'strong', color: '#28A745', text: 'Strong' };
    };

    const passwordStrength = getPasswordStrength();

    const handleChangePassword = async () => {
        // Validation
        if (!oldPassword.trim()) {
            Alert.alert('Validation Error', 'Please enter your current password');
            return;
        }

        if (!newPassword.trim()) {
            Alert.alert('Validation Error', 'Please enter a new password');
            return;
        }

        if (!confirmPassword.trim()) {
            Alert.alert('Validation Error', 'Please confirm your new password');
            return;
        }

        if (newPassword !== confirmPassword) {
            Alert.alert('Validation Error', 'New passwords do not match');
            return;
        }

        if (oldPassword === newPassword) {
            Alert.alert('Validation Error', 'New password must be different from current password');
            return;
        }

        const passwordValidation = validatePassword(newPassword);
        if (passwordValidation) {
            Alert.alert('Password Requirements', passwordValidation);
            return;
        }

        const auth = getAuth();
        const user = auth.currentUser;

        if (!user || !user.email) {
            Alert.alert('Error', 'User not authenticated');
            return;
        }

        setLoading(true);

        try {
            const credential = EmailAuthProvider.credential(user.email, oldPassword);
            await reauthenticateWithCredential(user, credential);
            await updatePassword(user, newPassword);

            Alert.alert(
                'Success',
                'Password updated successfully',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
            );
        } catch (error: any) {
            console.error('Password change error:', error);
            let errorMessage = 'Failed to update password';

            if (error.code === 'auth/wrong-password') {
                errorMessage = 'Current password is incorrect';
            } else if (error.code === 'auth/weak-password') {
                errorMessage = 'New password is too weak';
            } else if (error.code === 'auth/requires-recent-login') {
                errorMessage = 'Please log out and log back in before changing your password';
            }

            Alert.alert('Error', errorMessage);
        } finally {
            setLoading(false);
        }
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                {/* Header Section */}
                <View style={styles.headerContainer}>
                    <View style={styles.iconContainer}>
                        <Icon name="lock-closed" size={40} color="#3674B5" />
                    </View>
                    <Text style={styles.headerTitle}>Change Password</Text>
                    <Text style={styles.headerSubtitle}>
                        Update your password to keep your account secure
                    </Text>
                </View>

                {/* Form Container */}
                <View style={styles.formContainer}>
                    {/* Current Password */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Current Password</Text>
                        <View style={styles.passwordInputContainer}>
                            <TextInput
                                value={oldPassword}
                                onChangeText={setOldPassword}
                                style={styles.textInput}
                                mode="outlined"
                                secureTextEntry={!showOldPassword}
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                placeholder="Enter your current password"
                                autoCapitalize="none"
                            />
                            <TouchableOpacity
                                style={styles.eyeButton}
                                onPress={() => setShowOldPassword(!showOldPassword)}
                            >
                                <Icon
                                    name={showOldPassword ? 'eye-off' : 'eye'}
                                    size={20}
                                    color="#6C757D"
                                />
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* New Password */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>New Password</Text>
                        <View style={styles.passwordInputContainer}>
                            <TextInput
                                value={newPassword}
                                onChangeText={setNewPassword}
                                style={styles.textInput}
                                mode="outlined"
                                secureTextEntry={!showNewPassword}
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                placeholder="Enter your new password"
                                autoCapitalize="none"
                            />
                            <TouchableOpacity
                                style={styles.eyeButton}
                                onPress={() => setShowNewPassword(!showNewPassword)}
                            >
                                <Icon
                                    name={showNewPassword ? 'eye-off' : 'eye'}
                                    size={20}
                                    color="#6C757D"
                                />
                            </TouchableOpacity>
                        </View>

                        {/* Password Strength Indicator */}
                        {newPassword.length > 0 && (
                            <View style={styles.strengthContainer}>
                                <View style={styles.strengthBar}>
                                    <View
                                        style={[
                                            styles.strengthFill,
                                            {
                                                width: `${(Object.values(passwordRequirements).filter(Boolean).length / 5) * 100}%`,
                                                backgroundColor: passwordStrength.color
                                            }
                                        ]}
                                    />
                                </View>
                                {passwordStrength.text && (
                                    <Text style={[styles.strengthText, { color: passwordStrength.color }]}>
                                        {passwordStrength.text}
                                    </Text>
                                )}
                            </View>
                        )}
                    </View>

                    {/* Confirm Password */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Confirm New Password</Text>
                        <View style={styles.passwordInputContainer}>
                            <TextInput
                                value={confirmPassword}
                                onChangeText={setConfirmPassword}
                                style={styles.textInput}
                                mode="outlined"
                                secureTextEntry={!showConfirmPassword}
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                placeholder="Confirm your new password"
                                autoCapitalize="none"
                            />
                            <TouchableOpacity
                                style={styles.eyeButton}
                                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                            >
                                <Icon
                                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                                    size={20}
                                    color="#6C757D"
                                />
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Password Requirements */}
                    <View style={styles.requirementsContainer}>
                        <Text style={styles.requirementsTitle}>Password Requirements:</Text>
                        <View style={styles.requirementsList}>
                            <View style={styles.requirementItem}>
                                <Icon
                                    name={passwordRequirements.minLength ? "checkmark-circle" : "ellipse-outline"}
                                    size={16}
                                    color={passwordRequirements.minLength ? "#28A745" : "#DEE2E6"}
                                />
                                <Text style={[
                                    styles.requirementText,
                                    passwordRequirements.minLength && styles.requirementTextMet
                                ]}>
                                    At least 8 characters long
                                </Text>
                            </View>
                            <View style={styles.requirementItem}>
                                <Icon
                                    name={passwordRequirements.hasUpperCase ? "checkmark-circle" : "ellipse-outline"}
                                    size={16}
                                    color={passwordRequirements.hasUpperCase ? "#28A745" : "#DEE2E6"}
                                />
                                <Text style={[
                                    styles.requirementText,
                                    passwordRequirements.hasUpperCase && styles.requirementTextMet
                                ]}>
                                    Contains uppercase letter (A-Z)
                                </Text>
                            </View>
                            <View style={styles.requirementItem}>
                                <Icon
                                    name={passwordRequirements.hasLowerCase ? "checkmark-circle" : "ellipse-outline"}
                                    size={16}
                                    color={passwordRequirements.hasLowerCase ? "#28A745" : "#DEE2E6"}
                                />
                                <Text style={[
                                    styles.requirementText,
                                    passwordRequirements.hasLowerCase && styles.requirementTextMet
                                ]}>
                                    Contains lowercase letter (a-z)
                                </Text>
                            </View>
                            <View style={styles.requirementItem}>
                                <Icon
                                    name={passwordRequirements.hasNumbers ? "checkmark-circle" : "ellipse-outline"}
                                    size={16}
                                    color={passwordRequirements.hasNumbers ? "#28A745" : "#DEE2E6"}
                                />
                                <Text style={[
                                    styles.requirementText,
                                    passwordRequirements.hasNumbers && styles.requirementTextMet
                                ]}>
                                    Contains at least one number (0-9)
                                </Text>
                            </View>
                            <View style={styles.requirementItem}>
                                <Icon
                                    name={passwordRequirements.hasSpecialChar ? "checkmark-circle" : "ellipse-outline"}
                                    size={16}
                                    color={passwordRequirements.hasSpecialChar ? "#28A745" : "#DEE2E6"}
                                />
                                <Text style={[
                                    styles.requirementText,
                                    passwordRequirements.hasSpecialChar && styles.requirementTextMet
                                ]}>
                                    Contains special character (!@#$%^&*)
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Update Button */}
                    <TouchableOpacity
                        style={[styles.updateButton, loading && styles.updateButtonDisabled]}
                        onPress={handleChangePassword}
                        disabled={loading}
                        activeOpacity={0.8}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="white" />
                        ) : (
                            <Icon name="shield-checkmark" size={20} color="white" />
                        )}
                        <Text style={styles.updateButtonText}>
                            {loading ? 'Updating Password...' : 'Update Password'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    container: {
        flex: 1,
        paddingHorizontal: rm(20),
    },
    headerContainer: {
        alignItems: 'center',
        paddingVertical: rm(40),
        paddingHorizontal: rm(20),
    },
    iconContainer: {
        width: rm(80),
        height: rm(80),
        borderRadius: rm(40),
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: rm(20),
    },
    headerTitle: {
        fontSize: rm(28),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(8),
        textAlign: 'center',
    },
    headerSubtitle: {
        fontSize: rm(16),
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: rm(22),
    },
    formContainer: {
        backgroundColor: 'white',
        borderRadius: rm(16),
        padding: rm(24),
        marginBottom: rm(20),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: rm(2),
        },
        shadowOpacity: 0.1,
        shadowRadius: rm(8),
        elevation: 4,
    },
    inputGroup: {
        marginBottom: rm(24),
    },
    inputLabel: {
        fontSize: rm(16),
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: rm(8),
    },
    passwordInputContainer: {
        position: 'relative',
    },
    textInput: {
        backgroundColor: 'white',
        fontSize: rm(16),
    },
    inputContent: {
        fontSize: rm(16),
        color: '#2C3E50',
        paddingRight: rm(50), // Space for eye button
    },
    eyeButton: {
        position: 'absolute',
        right: rm(12),
        top: rm(16),
        padding: rm(8),
    },
    requirementsContainer: {
        backgroundColor: '#F8F9FA',
        borderRadius: rm(12),
        padding: rm(16),
        marginBottom: rm(24),
        borderLeftWidth: rm(4),
        borderLeftColor: '#3674B5',
    },
    requirementsTitle: {
        fontSize: rm(14),
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: rm(12),
    },
    requirementsList: {
        gap: rm(8),
    },
    requirementItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: rm(8),
    },
    requirementText: {
        fontSize: rm(14),
        color: '#6C757D',
        flex: 1,
    },
    requirementTextMet: {
        color: '#28A745',
        fontWeight: '500',
    },
    strengthContainer: {
        marginTop: rm(12),
        flexDirection: 'row',
        alignItems: 'center',
        gap: rm(12),
    },
    strengthBar: {
        flex: 1,
        height: rm(4),
        backgroundColor: '#E9ECEF',
        borderRadius: rm(2),
        overflow: 'hidden',
    },
    strengthFill: {
        height: '100%',
        borderRadius: rm(2),
        transition: 'width 0.3s ease',
    },
    strengthText: {
        fontSize: rm(12),
        fontWeight: '600',
        minWidth: rm(50),
        textAlign: 'right',
    },
    updateButton: {
        backgroundColor: '#3674B5',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: rm(16),
        borderRadius: rm(12),
        shadowColor: '#3674B5',
        shadowOffset: {
            width: 0,
            height: rm(4),
        },
        shadowOpacity: 0.3,
        shadowRadius: rm(8),
        elevation: 6,
        gap: rm(8),
    },
    updateButtonDisabled: {
        backgroundColor: '#6C757D',
        shadowOpacity: 0.1,
    },
    updateButtonText: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: 'white',
    },
});

export default ChangePassword;
