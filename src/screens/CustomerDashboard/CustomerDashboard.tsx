import React, { useState, useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View, ActivityIndicator, StyleSheet, TextInput, RefreshControl } from 'react-native';
import { Paths } from '@/navigation/paths';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import { useTranslation } from 'react-i18next';
import type { RootScreenProps } from '@/navigation/types';
import DockCard from '@/components/common/DockCard';
import Icon from 'react-native-vector-icons/Ionicons';

import { getAuth, onAuthStateChanged, User } from 'firebase/auth';
import { collection, query, where, getDocs, orderBy, limit } from 'firebase/firestore';
import { db, app } from '@/config/firebase';
import { isFutureDate } from '@/utils/bookingUtils';
import { useFocusEffect } from '@react-navigation/native';
import { rm } from '@/utils/scaling';

const auth = getAuth(app);

// ✅ Type as an array of docks
interface DockDetail {
    id: string;
    name: string;
    description?: string;
    location?: string;
    userId?: string;
    imageUrl?: string;
    imageUrls?: string[];
    // Only use dateTimeSlots
    dateTimeSlots?: { [date: string]: any[] };
    price?: number;
    type?: string;
    createdAt?: any;
}

function CustomerDashboard({ navigation }: RootScreenProps<Paths.CustomerDashboard>) {
    const { layout, backgrounds } = useTheme();
    const { t } = useTranslation();
    const [dockDetails, setDockDetails] = useState<DockDetail[]>([]);
    const [filteredDocks, setFilteredDocks] = useState<DockDetail[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        const unsubscribeAuth = onAuthStateChanged(auth, user => {
            setCurrentUser(user);
            if (!user) setLoading(false);
        });

        return () => unsubscribeAuth();
    }, []);


    // Search functionality
    useEffect(() => {
        if (searchQuery.trim() === '') {
            setFilteredDocks(dockDetails);
        } else {
            const filtered = dockDetails.filter(dock =>
                dock.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                dock.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
                dock.location?.toLowerCase().includes(searchQuery.toLowerCase())
            );
            setFilteredDocks(filtered);
        }
    }, [searchQuery, dockDetails]);

    const fetchDockDetails = async () => {
        try {
            setLoading(true);
            setError(null);
            // Simple query without orderBy to test basic functionality
            const querySnapshot = await getDocs(collection(db, 'docks'));

            if (!querySnapshot.empty) {
                const docks: DockDetail[] = querySnapshot.docs.map(doc => {
                    const data = doc.data();
                    return {
                        id: doc.id,
                        ...(data as Omit<DockDetail, 'id'>),
                    };
                });

                // Filter docks to show only those with future dates (from today onwards)
                const futureDocks = docks.filter(dock => {
                    // Check if dock has dateTimeSlots
                    if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
                        // Get all dates from dateTimeSlots
                        const availableDates = Object.keys(dock.dateTimeSlots);

                        // Check if any date is in the future
                        return availableDates.some(dateString => {
                            // Parse date properly to avoid timezone issues
                            const dockDate = new Date(dateString + 'T12:00:00');
                            return isFutureDate(dockDate);
                        });
                    }

                    return false; // No date information available
                });
                setDockDetails(futureDocks);
                setFilteredDocks(futureDocks);
            } else {
                console.log('No docks found in database');
                setDockDetails([]);
                setFilteredDocks([]);
            }
        } catch (err: any) {
            console.error('Error fetching docks:', err);
            console.error('Error details:', err.message, err.code);
            setError(`Failed to load docks: ${err.message}`);
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            if (currentUser) {
                fetchDockDetails();
            } else if (!loading) {
                setDockDetails([]);
                setFilteredDocks([]);
                setLoading(false);
            }
        }, [currentUser])
    );

    const handleRefresh = () => {
        setRefreshing(true);
        fetchDockDetails();
    };

    // if (loading) {
    //     return (
    //         // <SafeScreen>
    //         <View style={[layout.flex_1, layout.col, layout.justifyCenter, layout.itemsCenter, backgrounds.white]}>
    //             <ActivityIndicator size="large" color="#3674B5" />
    //             <Text>Loading dock details...</Text>
    //         </View>
    //         // </SafeScreen>
    //     );
    // }

    if (error) {
        return (
            <SafeScreen>
                <View style={[layout.flex_1, layout.col, layout.justifyCenter, layout.itemsCenter, backgrounds.white]}>
                    <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
                </View>
            </SafeScreen>
        );
    }

    return (
        // <SafeScreen>
        <View style={[layout.flex_1, backgrounds.white]}>
            {/* Header Section */}
            <View style={styles.headerSection}>
                <View style={styles.welcomeContainer}>
                    <Text style={styles.welcomeText}>Find Your Perfect Dock</Text>
                    <Text style={styles.subtitleText}>Discover and book available docks near you</Text>
                </View>

                {/* Search Bar */}
                <View style={styles.searchContainer}>
                    <Icon name="search" size={20} color="#6C757D" style={styles.searchIcon} />
                    <TextInput
                        style={styles.searchInput}
                        placeholder="Search docks by name, location..."
                        value={searchQuery}
                        onChangeText={setSearchQuery}
                        placeholderTextColor="#6C757D"
                    />
                    {searchQuery.length > 0 && (
                        <TouchableOpacity onPress={() => setSearchQuery('')} style={styles.clearButton}>
                            <Icon name="close-circle" size={20} color="#6C757D" />
                        </TouchableOpacity>
                    )}
                </View>

                {/* Stats Bar */}
                <View style={styles.statsContainer}>
                    <View style={styles.statItem}>
                        <Icon name="boat-outline" size={16} color="#3674B5" />
                        <Text style={styles.statText}>
                            {filteredDocks.length} dock{filteredDocks.length !== 1 ? 's' : ''} available
                        </Text>
                    </View>
                    {searchQuery.length > 0 && (
                        <Text style={styles.searchResultText}>
                            Showing results for "{searchQuery}"
                        </Text>
                    )}
                </View>
            </View>

            {/* Docks List */}
            <ScrollView
                style={styles.docksContainer}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={refreshing}
                        onRefresh={handleRefresh}
                        colors={['#3674B5']}
                        tintColor="#3674B5"
                    />
                }
            >
                {filteredDocks.length > 0 ? (
                    filteredDocks.map(dock => {
                        // Determine the display date for the dock card from dateTimeSlots
                        let displayDate = null;
                        if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
                            // Get all dates and find the earliest future date
                            const availableDates = Object.keys(dock.dateTimeSlots);
                            const futureDates = availableDates
                                .map(dateString => new Date(dateString + 'T12:00:00'))
                                .filter(date => isFutureDate(date))
                                .sort((a, b) => a.getTime() - b.getTime());

                            if (futureDates.length > 0) {
                                displayDate = futureDates[0];
                            }
                        }

                        return (
                            <DockCard
                                key={dock.id}
                                dock={{
                                    ...dock,
                                    imageUrl: dock.imageUrls?.[0] || dock.imageUrl || '',
                                    description: dock.description || '',
                                    availableDate: displayDate,
                                    price: dock.price?.toString() || '0',
                                    // Pass dateTimeSlots for enhanced display
                                    dateTimeSlots: dock.dateTimeSlots
                                }}
                            />
                        );
                    })
                ) : loading ? (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color="#3674B5" />
                        <Text style={styles.loadingText}>Loading available docks...</Text>
                    </View>
                ) : (
                    <View style={styles.emptyContainer}>
                        <Icon name="boat-outline" size={64} color="#ADB5BD" />
                        <Text style={styles.emptyTitle}>
                            {searchQuery ? 'No docks found' : 'No docks available'}
                        </Text>
                        <Text style={styles.emptySubtitle}>
                            {searchQuery
                                ? 'Try adjusting your search terms'
                                : 'Check back later for new dock listings'
                            }
                        </Text>
                        {searchQuery && (
                            <TouchableOpacity
                                style={styles.clearSearchButton}
                                onPress={() => setSearchQuery('')}
                            >
                                <Text style={styles.clearSearchText}>Clear Search</Text>
                            </TouchableOpacity>
                        )}
                    </View>
                )}
            </ScrollView>
        </View>
        // </SafeScreen>
    );
}

const styles = StyleSheet.create({
    headerSection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingTop: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F1F3F4',
    },
    welcomeContainer: {
        marginBottom: 20,
    },
    welcomeText: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 4,
    },
    subtitleText: {
        fontSize: 16,
        color: '#6C757D',
        lineHeight: 22,
    },
    searchContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        borderRadius: 12,
        paddingHorizontal: 16,
        paddingVertical: 12,
        marginBottom: 16,
        borderWidth: 1,
        borderColor: '#E9ECEF',
    },
    searchIcon: {
        marginRight: 12,
    },
    searchInput: {
        flex: 1,
        fontSize: 16,
        color: '#2C3E50',
        paddingVertical: 0,
    },
    clearButton: {
        marginLeft: 8,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statText: {
        fontSize: 14,
        color: '#3674B5',
        fontWeight: '600',
        marginLeft: 6,
    },
    searchResultText: {
        fontSize: 12,
        color: '#6C757D',
        fontStyle: 'italic',
    },
    docksContainer: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 60,
    },
    loadingText: {
        marginTop: 16,
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
        paddingVertical: 60,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: 22,
        marginBottom: 24,
    },
    clearSearchButton: {
        backgroundColor: '#3674B5',
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 8,
    },
    clearSearchText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default CustomerDashboard;
