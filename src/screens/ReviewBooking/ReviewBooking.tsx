import React, { useState } from 'react';
import {
    StyleSheet,
    View,
    Text,
    ScrollView,
    Image,
    TouchableOpacity,
    Alert,
    SafeAreaView,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/Ionicons'; // For back arrow and edit icon
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import { auth, db } from '@/config/firebase';
import { addDoc, collection, doc, getDocs, query, Timestamp, where } from 'firebase/firestore';
import { rm } from '@/utils/scaling';

const Colors = {
    PrimaryColor: '#3674B5', // Example blue
    AccentColor: '#ffc107', // Example yellow/gold for best rated
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Approved: '#28a745',
    Border: '#dee2e6',
    StrikeThrough: '#999999', // Color for strikethrough price
};

// Mock Data for the screen (you would pass this from the previous booking screen)
// const mockBookingDetails = {
//     dock: {
//         type: 'Fixed Dock',
//         name: 'Dock Michelin',
//         location: '12345. Torantaro Cost, NYC',
//         imageUrl: 'https://placehold.co/100x80/000000/ffffff?text=Dock',
//         isBestRated: true,
//     },
//     booking: {
//         dates: 'June 12-13',
//         dockType: 'Fixed Dock', // This might be redundant if already in dock object
//         originalCost: 150.87,
//         discountPercentage: 10,
//     },
// };

function ReviewBooking({ navigation }: RootScreenProps<Paths.ReviewBooking>) {
    const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<'credit' | 'debit' | null>(null);
    const route = useRoute();
    const { dock, booking } = route.params;

    // const discountedCost = booking.originalCost * (1 - booking.discountPercentage / 100);

    // Calculate total price based on selected slots
    const calculateTotalPrice = () => {
        const basePrice = parseFloat(dock.price) || 0;
        const numberOfSlots = booking.selectedSlots?.length || 0;
        return basePrice * numberOfSlots;
    };

    // Get selected time slot details with dates
    const getSelectedSlotDetails = () => {
        if (!dock.timeSlots || !booking.selectedSlots) return [];
        return dock.timeSlots.filter((slot: any) => booking.selectedSlots.includes(slot.id));
    };

    // Get formatted date ranges for display
    const getFormattedDateRanges = () => {
        if (!booking.selectedDates || booking.selectedDates.length === 0) return 'No dates selected';

        const sortedDates = booking.selectedDates.sort();
        if (sortedDates.length === 1) {
            return new Date(sortedDates[0]).toLocaleDateString('en-US', {
                weekday: 'long',
                day: '2-digit',
                month: 'long',
                year: 'numeric'
            });
        } else {
            const firstDate = new Date(sortedDates[0]).toLocaleDateString('en-US', {
                day: '2-digit',
                month: 'short'
            });
            const lastDate = new Date(sortedDates[sortedDates.length - 1]).toLocaleDateString('en-US', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });
            return `${firstDate} - ${lastDate} (${sortedDates.length} dates)`;
        }
    };

    const checkExistingBooking = async (dockId: string, selectedSlotIds: string[], selectedDates?: string[], selectedDateSlots?: { [date: string]: string[] }) => {
        const bookingRef = collection(db, "booking");

        // For multi-date bookings, check date-specific conflicts
        if (selectedDates && selectedDateSlots) {
            for (const dateString of selectedDates) {
                const slotsForDate = selectedDateSlots[dateString] || [];

                for (const slotId of slotsForDate) {
                    // Check for conflicts on specific date and slot combination
                    const q = query(
                        bookingRef,
                        where("dockId", "==", dockId),
                        where("selectedSlots", "array-contains", slotId)
                    );
                    const querySnapshot = await getDocs(q);

                    // Additional check for date-specific conflicts if the booking has date information
                    for (const doc of querySnapshot.docs) {
                        const existingBooking = doc.data();

                        // If existing booking has date information, check for date overlap
                        if (existingBooking.selectedDates && Array.isArray(existingBooking.selectedDates)) {
                            if (existingBooking.selectedDates.includes(dateString)) {
                                return true; // Found a date-specific conflict
                            }
                        } else {
                            // Legacy booking without date info - assume conflict
                            return true;
                        }
                    }
                }
            }
        } else {
            // Fallback to original slot-only check for legacy bookings
            for (const slotId of selectedSlotIds) {
                const q = query(
                    bookingRef,
                    where("dockId", "==", dockId),
                    where("selectedSlots", "array-contains", slotId)
                );
                const querySnapshot = await getDocs(q);
                if (!querySnapshot.empty) {
                    return true; // Found a conflict
                }
            }
        }

        return false; // No conflicts found
    };

    const handlePay = () => {
        const totalPrice = calculateTotalPrice();
        const numberOfSlots = booking.selectedSlots?.length || 0;
        const numberOfDates = booking.selectedDates?.length || 0;

        // Show payment confirmation alert
        Alert.alert(
            "Confirm Payment",
            `Do you want to proceed with the payment of $${totalPrice.toFixed(2)} for ${numberOfDates} date${numberOfDates > 1 ? 's' : ''} and ${numberOfSlots} slot${numberOfSlots > 1 ? 's' : ''}?`,
            [
                {
                    text: "Cancel",
                    style: "cancel"
                },
                {
                    text: "OK",
                    onPress: () => processPayment()
                }
            ]
        );
    };

    const processPayment = async () => {
        const selectedSlotDetails = getSelectedSlotDetails();
        const totalPrice = calculateTotalPrice();

        // Convert Date objects to Firebase Timestamps to preserve timezone
        const processedSlotDetails = selectedSlotDetails.map((slot: any) => {

            const processed = {
                id: slot.id,
                startTime: slot.startTime instanceof Date ? Timestamp.fromDate(slot.startTime) : slot.startTime,
                endTime: slot.endTime instanceof Date ? Timestamp.fromDate(slot.endTime) : slot.endTime,
            };

            return processed;
        });

        const data = {
            bookingUserId: auth?.currentUser?.uid,
            name: dock.name,
            availableDate: dock.availableDate, // Keep for backward compatibility
            selectedDates: booking.selectedDates, // Array of selected date strings
            selectedDateSlots: booking.selectedDateSlots, // Object mapping dates to slot arrays
            selectedSlots: booking.selectedSlots, // Array of slot IDs
            selectedSlotDetails: processedSlotDetails, // Full slot details with proper timestamps
            pricePerSlot: parseFloat(dock.price),
            totalPrice: totalPrice,
            numberOfSlots: booking.selectedSlots?.length || 0,
            numberOfDates: booking.selectedDates?.length || 0,
            dockId: dock.dockId,
            dockImageUrl: dock.imageUrls?.[0] || "",
            status: 'Approved',
            // Include booking details
            customerDetails: {
                fullName: booking.fullName,
                contact: booking.contact,
                email: booking.email,
                boatName: booking.boatName,
                boatType: booking.boatType,
                boatLength: booking.boatLength,
                boatWidth: booking.boatWidth,
                registrationNumber: booking.registrationNumber,
                message: booking.message,
            }
        };

        const bookingExists = await checkExistingBooking(
            dock.dockId,
            booking.selectedSlots,
            booking.selectedDates,
            booking.selectedDateSlots
        );
        if (bookingExists) {
            Alert.alert("Booking Error", "One or more of the selected time slots have already been booked for the selected dates.");
            return;
        }

        try {
            const docRef = await addDoc(collection(db, 'booking'), {
                ...data,
                createdAt: Timestamp.now(),
            });
            // Show success notification and navigate
            Alert.alert(
                "Payment Successful!",
                "Your payment has been processed successfully and your booking has been confirmed. You will receive a confirmation email shortly.",
                [
                    {
                        text: "OK",
                        onPress: () => navigation.navigate(Paths.MainTabs)
                    }
                ]
            );

        } catch (e) {
            console.log("Data", data)
            console.error("Error adding document: ", e);
            Alert.alert(
                "Payment Failed",
                "There was an error processing your payment. Please try again or contact support if the problem persists.",
                [{ text: "OK" }]
            );
        }
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <View style={styles.container}>
                {/* Header */}

                <ScrollView contentContainerStyle={styles.scrollViewContent}>
                    {/* Overview Section */}
                    <Text style={styles.sectionHeading}>Overview</Text>
                    <View style={styles.card}>
                        <View style={styles.overviewDockInfo}>
                            <Image
                                source={{ uri: dock.imageUrl }}
                                style={styles.overviewDockImage}
                                resizeMode="cover"
                            />
                            <View style={styles.overviewTextContent}>
                                <Text style={styles.overviewDockType}>{dock.type}</Text>
                                <Text style={styles.overviewDockName}>{dock.name}</Text>
                                <View style={styles.locationContainer}>
                                    <Text style={styles.overviewDockLocation}>📍{dock.address}</Text>
                                </View>
                                {dock.isBestRated && (
                                    <View style={styles.bestRatedBadge}>
                                        <Text style={styles.bestRatedText}>🏅 Best Rated</Text>
                                    </View>
                                )}
                            </View>
                        </View>
                    </View>

                    {/* Your Booking Section */}
                    <Text style={styles.sectionHeading}>Your Booking</Text>
                    <View style={styles.card}>
                        <View style={styles.bookingDetailRow}>
                            <Text style={styles.bookingLabel}>Selected Dates</Text>
                            <Text style={styles.bookingValue}>
                                {getFormattedDateRanges()}
                            </Text>
                        </View>

                        <View style={styles.bookingDetailRow}>
                            <Text style={styles.bookingLabel}>Selected Time Slots</Text>
                            <View style={styles.timeSlotsContainer}>
                                {booking.selectedDates && booking.selectedDateSlots ? (
                                    booking.selectedDates.map((dateString: string) => (
                                        <View key={dateString} style={styles.dateSlotGroup}>
                                            <Text style={styles.dateSlotGroupTitle}>
                                                {new Date(dateString).toLocaleDateString('en-US', {
                                                    weekday: 'short',
                                                    day: '2-digit',
                                                    month: 'short'
                                                })}
                                            </Text>
                                            <View style={styles.dateSlotChips}>
                                                {booking.selectedDateSlots[dateString]?.map((slotId: string) => {
                                                    // Find slot in date-specific slots first, then fallback to general timeSlots
                                                    let slot = null;
                                                    if (dock.dateTimeSlots && dock.dateTimeSlots[dateString]) {
                                                        slot = dock.dateTimeSlots[dateString].find((s: any) => s.id === slotId);
                                                    }
                                                    if (!slot && dock.timeSlots) {
                                                        slot = dock.timeSlots.find((s: any) => s.id === slotId);
                                                    }

                                                    if (!slot) return null;
                                                    return (
                                                        <View key={`${dateString}-${slotId}`} style={styles.timeSlotChip}>
                                                            <Icon name="time-outline" size={rm(12)} color={Colors.PrimaryColor} />
                                                            <Text style={styles.timeSlotText}>
                                                                {slot.startTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })} - {slot.endTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                                            </Text>
                                                        </View>
                                                    );
                                                })}
                                            </View>
                                        </View>
                                    ))
                                ) : (
                                    // Fallback for backward compatibility
                                    getSelectedSlotDetails().map((slot: any, index: number) => (
                                        <View key={slot.id || index} style={styles.timeSlotChip}>
                                            <Icon name="time-outline" size={rm(14)} color={Colors.PrimaryColor} />
                                            <Text style={styles.timeSlotText}>
                                                {slot.startTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })} - {slot.endTime?.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true })}
                                            </Text>
                                        </View>
                                    ))
                                )}
                            </View>
                        </View>

                        <View style={styles.bookingDetailRow}>
                            <Text style={styles.bookingLabel}>Dock Type</Text>
                            <Text style={styles.bookingValue}>{dock.type}</Text>
                        </View>

                        <View style={styles.bookingDetailRow}>
                            <Text style={styles.bookingLabel}>Number of Slots</Text>
                            <Text style={styles.bookingValue}>{booking.selectedSlots?.length || 0}</Text>
                        </View>
                    </View>

                    {/* Payment Information Section */}
                    <Text style={styles.sectionHeading}>Payment Information</Text>
                    <View style={styles.card}>
                        <View style={styles.priceBreakdown}>
                            <View style={styles.priceRow}>
                                <Text style={styles.priceLabel}>Price per slot:</Text>
                                <Text style={styles.priceValue}>${dock.price.toFixed(2)}</Text>
                            </View>
                            <View style={styles.priceRow}>
                                <Text style={styles.priceLabel}>Number of slots:</Text>
                                <Text style={styles.priceValue}>{booking.selectedSlots?.length || 0}</Text>
                            </View>
                            <View style={[styles.priceRow, styles.totalRow]}>
                                <Text style={styles.totalLabel}>Total:</Text>
                                <Text style={styles.totalValue}>${calculateTotalPrice().toFixed(2)}</Text>
                            </View>
                        </View>
                    </View>

                    {/* Choose Payment Method Section */}

                    {/* Spacer for bottom bar */}
                    <View style={{ height: rm(100) }} />
                </ScrollView >

                {/* Bottom Fixed Bar */}
                <View style={styles.bottomBar}>
                    <View style={styles.bottomPriceContainer}>
                        <Text style={styles.finalPriceText}>$ {calculateTotalPrice().toFixed(2)}</Text>
                        <Text style={styles.bottomTotalText}>
                            {booking.selectedDates?.length || 0} date{(booking.selectedDates?.length || 0) > 1 ? 's' : ''}, {booking.selectedSlots?.length || 0} slot{(booking.selectedSlots?.length || 0) > 1 ? 's' : ''}
                        </Text>
                    </View>
                    <TouchableOpacity style={styles.payButton} onPress={handlePay}>
                        <Text style={styles.payButtonText}>Pay</Text>
                    </TouchableOpacity>
                </View>
            </View >
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: 'white',
    },
    container: {
        flex: 1,
        backgroundColor: Colors.LightGray,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: rm(15),
        paddingVertical: rm(15),
        backgroundColor: Colors.White,
        paddingTop: rm(40), // Adjust for status bar/notch
        borderBottomWidth: 1,
        borderBottomColor: Colors.Border,
    },
    backButton: {
        padding: rm(5),
    },
    headerTitle: {
        fontSize: rm(20),
        fontWeight: 'bold',
        color: Colors.Dark,
        marginLeft: rm(10),
    },
    scrollViewContent: {
        paddingVertical: rm(15),
    },
    sectionHeading: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginLeft: rm(15),
        marginBottom: rm(10),
        marginTop: rm(15),
    },
    card: {
        backgroundColor: Colors.White,
        borderRadius: rm(10),
        marginHorizontal: rm(15),
        padding: rm(20),
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 3,
        marginBottom: rm(10),
    },
    // Overview Card Styles
    overviewDockInfo: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    overviewDockImage: {
        width: rm(100),
        height: rm(80),
        borderRadius: rm(8),
        marginRight: rm(15),
    },
    overviewTextContent: {
        flex: 1,
    },
    overviewDockType: {
        fontSize: rm(12),
        color: Colors.Gray,
        marginBottom: rm(2),
    },
    overviewDockName: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(5),
    },
    locationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: rm(5),
    },
    overviewDockLocation: {
        fontSize: rm(13),
        color: Colors.Gray,
        marginLeft: rm(5),
    },
    bestRatedBadge: {
        backgroundColor: Colors.AccentColor,
        borderRadius: rm(5),
        paddingVertical: rm(3),
        paddingHorizontal: rm(8),
        alignSelf: 'flex-start',
    },
    bestRatedText: {
        fontSize: rm(11),
        fontWeight: 'bold',
        color: Colors.Dark,
    },

    // Your Booking Card Styles
    bookingDetailRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: rm(8),
        borderBottomWidth: 1,
        borderBottomColor: Colors.Border,
    },
    // Ensure the last row doesn't have a bottom border if preferred
    lastBookingDetailRow: {
        borderBottomWidth: 0,
    },
    bookingLabel: {
        fontSize: rm(14),
        color: Colors.Gray,
    },
    bookingValue: {
        fontSize: rm(14),
        color: Colors.Dark,
        fontWeight: '500',
    },

    // Payment Information Card Styles
    paymentInfoTotalCost: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(5),
    },
    originalCost: {
        // textDecorationLine: 'line-through',
        color: Colors.StrikeThrough,
        marginRight: rm(5),
    },
    discountedCost: {
        color: Colors.PrimaryColor,
    },
    discountApplied: {
        fontSize: rm(13),
        color: Colors.Approved,
    },

    // Payment Method Selection Styles
    paymentMethodOption: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: rm(15),
        paddingHorizontal: rm(10),
    },
    selectedOption: {
        // You can add a background or border change here if desired for selection feedback
        // backgroundColor: Colors.LightGray,
        // borderRadius: rm(8),
    },
    cardLogo: {
        width: rm(40),
        height: rm(25),
        resizeMode: 'contain',
        marginRight: rm(15),
    },
    cardNumberText: {
        flex: 1,
        fontSize: rm(16),
        color: Colors.Dark,
    },
    radioCircle: {
        height: rm(20),
        width: rm(20),
        borderRadius: rm(10),
        borderWidth: 2,
        borderColor: Colors.Gray,
        alignItems: 'center',
        justifyContent: 'center',
    },
    selectedRadioFill: {
        width: rm(12),
        height: rm(12),
        borderRadius: rm(6),
        backgroundColor: Colors.PrimaryColor,
    },
    separator: {
        height: 1,
        backgroundColor: Colors.Border,
        marginVertical: rm(5),
        marginHorizontal: -rm(20), // Extend separator to card edges
    },

    // Bottom Fixed Bar Styles
    bottomBar: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: Colors.White,
        paddingVertical: rm(15),
        paddingHorizontal: rm(20),
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
        elevation: 5,
        position: 'absolute',
        bottom: 0,
        width: '100%',
    },
    bottomPriceContainer: {
        flexDirection: 'row',
        alignItems: 'baseline',
    },
    finalPriceText: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: Colors.Heading,
    },
    bottomTotalText: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginLeft: rm(5),
    },
    payButton: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(10),
        paddingVertical: rm(15),
        paddingHorizontal: rm(40),
    },
    payButtonText: {
        color: Colors.White,
        fontSize: rm(18),
        fontWeight: 'bold',
    },
    timeSlotsContainer: {
        flexDirection: 'column',
        gap: rm(12),
        marginTop: rm(8),
    },
    // Date slot group styles
    dateSlotGroup: {
        marginBottom: rm(8),
    },
    dateSlotGroupTitle: {
        fontSize: rm(13),
        fontWeight: '600',
        color: Colors.Heading,
        marginBottom: rm(6),
    },
    dateSlotChips: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: rm(6),
    },
    timeSlotChip: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.LightGray,
        borderRadius: rm(15),
        paddingVertical: rm(6),
        paddingHorizontal: rm(12),
        borderWidth: 1,
        borderColor: Colors.PrimaryColor,
        marginBottom: rm(4),
    },
    timeSlotText: {
        fontSize: rm(12),
        color: Colors.PrimaryColor,
        fontWeight: '500',
        marginLeft: rm(4),
    },
    priceBreakdown: {
        paddingVertical: rm(10),
    },
    priceRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingVertical: rm(8),
    },
    priceLabel: {
        fontSize: rm(14),
        color: Colors.Dark,
    },
    priceValue: {
        fontSize: rm(14),
        color: Colors.Dark,
        fontWeight: '500',
    },
    totalRow: {
        borderTopWidth: 1,
        borderTopColor: Colors.Border,
        marginTop: rm(8),
        paddingTop: rm(12),
    },
    totalLabel: {
        fontSize: rm(16),
        color: Colors.Heading,
        fontWeight: 'bold',
    },
    totalValue: {
        fontSize: rm(18),
        color: Colors.PrimaryColor,
        fontWeight: 'bold',
    },
});

export default ReviewBooking;