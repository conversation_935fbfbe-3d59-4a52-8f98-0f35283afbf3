import React, { useState, useEffect } from 'react';
import { ScrollView, Text, TouchableOpacity, View, ActivityIndicator, StyleSheet, Alert, RefreshControl } from 'react-native';
import { Paths } from '@/navigation/paths';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import { useTranslation } from 'react-i18next';
import type { RootScreenProps } from '@/navigation/types';
import DockCard from '@/components/common/DockCard';
import Fab from '@/components/common/Fab/Index';
import Icon from 'react-native-vector-icons/Ionicons';

import { getAuth, onAuthStateChanged, User } from 'firebase/auth';
import { collection, query, where, getDocs, doc, deleteDoc, orderBy } from 'firebase/firestore';
import { db, app } from '@/config/firebase';
import { isFutureDate } from '@/utils/bookingUtils';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { rm } from '@/utils/scaling';

const auth = getAuth(app);

// ✅ Type as an array of docks
interface DockDetail {
    id: string;
    name: string;
    description?: string;
    location?: string;
    userId?: string;
    imageUrl?: string;
    imageUrls?: string[];
    // Only use dateTimeSlots
    dateTimeSlots?: { [date: string]: any[] };
    price?: number;
    type?: string;
    createdAt?: any;
}

function OwnerDashboard({ }: RootScreenProps<Paths.OwnerDashboard>) {
    const navigation = useNavigation();
    const { layout, backgrounds } = useTheme();
    const { t } = useTranslation();
    const [reload, setReload] = useState(false);
    const [dockDetails, setDockDetails] = useState<DockDetail[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [refreshing, setRefreshing] = useState(false);

    useEffect(() => {
        const unsubscribeAuth = onAuthStateChanged(auth, user => {
            setCurrentUser(user);
            if (!user) setLoading(false);
        });
        return () => unsubscribeAuth();
    }, []);

    const deleteDockById = async (id: string) => {
        Alert.alert(
            'Delete Dock',
            'Are you sure you want to delete this dock? This action cannot be undone.',
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: async () => {
                        try {
                            await deleteDoc(doc(db, 'docks', id));
                            setReload(prev => !prev);
                            Alert.alert('Success', 'Dock deleted successfully');
                        } catch (error) {
                            console.error('Error deleting document:', error);
                            Alert.alert('Error', 'Failed to delete dock. Please try again.');
                        }
                    },
                },
            ]
        );
    };


    const fetchDockDetails = async () => {
        if (!currentUser) {
            if (!loading) {
                setDockDetails([]);
                setLoading(false);
            }
            return;
        }

        try {
            setLoading(true);
            setError(null);

            const userId = currentUser.uid;

            // Simple query without orderBy to test basic functionality
            const q = query(
                collection(db, 'docks'),
                where('userId', '==', userId)
            );
            const querySnapshot = await getDocs(q);

            if (!querySnapshot.empty) {
                const docks: DockDetail[] = querySnapshot.docs.map(doc => {
                    const data = doc.data();
                    console.log("Data:", JSON.stringify(data));
                    return {
                        id: doc.id,
                        ...(data as Omit<DockDetail, 'id'>),
                    };
                });
                // Filter docks to show only those with future dates (from today onwards)
                const futureDocks = docks.filter(dock => {
                    // Check if dock has dateTimeSlots
                    if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
                        // Get all dates from dateTimeSlots
                        const availableDates = Object.keys(dock.dateTimeSlots);
                        console.log(`Dock ${dock.name} has dates:`, availableDates);

                        // Check if any date is in the future
                        return availableDates.some(dateString => {
                            // Parse date properly to avoid timezone issues
                            const dockDate = new Date(dateString + 'T12:00:00');
                            const isFuture = isFutureDate(dockDate);
                            console.log(`Checking date: ${dateString} -> ${dockDate.toLocaleDateString()} -> isFuture: ${isFuture}`);
                            return isFuture;
                        });
                    }

                    console.log(`Dock ${dock.name} has no dateTimeSlots`);
                    return false; // No date information available
                });

                console.log("Filtered future docks:", futureDocks.length);
                console.log("Future docks:", futureDocks.map(d => ({ name: d.name, dateTimeSlots: Object.keys(d.dateTimeSlots || {}) })));
                setDockDetails(futureDocks);
            } else {
                console.log('No docks found for this owner');
                setDockDetails([]);
            }
        } catch (err: any) {
            console.error('Error fetching docks:', err);
            setError('Failed to load your docks.');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    useFocusEffect(
        React.useCallback(() => {
            console.log('OwnerDashboard focused, fetching data...');
            fetchDockDetails();
        }, [currentUser, reload])
    );

    const handleRefresh = () => {
        setRefreshing(true);
        fetchDockDetails();
    };

    // if (loading) {
    //     return (
    //         // <SafeScreen>
    //         <View style={[layout.flex_1, layout.col, layout.justifyCenter, layout.itemsCenter, backgrounds.white]}>
    //             <ActivityIndicator size="large" color="#3674B5" />
    //             <Text>Loading dock details...</Text>
    //         </View>
    //         // </SafeScreen>
    //     );
    // }

    if (error) {
        return (
            <SafeScreen>
                <View style={[layout.flex_1, layout.col, layout.justifyCenter, layout.itemsCenter, backgrounds.white]}>
                    <Text style={{ color: 'red', textAlign: 'center' }}>{error}</Text>
                </View>
            </SafeScreen>
        );
    }

    return (
        // <SafeScreen>
        <View style={[layout.flex_1, backgrounds.white]}>
            {/* Header Section */}
            <View style={styles.headerSection}>
                <View style={styles.welcomeContainer}>
                    <Text style={styles.welcomeText}>Manage Your Docks</Text>
                    <Text style={styles.subtitleText}>
                        {dockDetails.length > 0
                            ? `You have ${dockDetails.length} dock${dockDetails.length > 1 ? 's' : ''} listed`
                            : 'Start by adding your first dock'
                        }
                    </Text>
                </View>

                {/* Stats Bar */}
                {dockDetails.length > 0 && (
                    <View style={styles.statsContainer}>
                        <View style={styles.statItem}>
                            <Icon name="boat-outline" size={16} color="#3674B5" />
                            <Text style={styles.statText}>
                                {dockDetails.length} Active Dock{dockDetails.length > 1 ? 's' : ''}
                            </Text>
                        </View>
                        <TouchableOpacity onPress={handleRefresh} style={styles.refreshButton}>
                            <Icon name="refresh" size={16} color="#3674B5" />
                        </TouchableOpacity>
                    </View>
                )}
            </View>

            {/* Content */}
            {dockDetails.length > 0 ? (
                <ScrollView
                    style={styles.docksContainer}
                    showsVerticalScrollIndicator={false}
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={handleRefresh}
                            colors={['#3674B5']}
                            tintColor="#3674B5"
                        />
                    }
                >
                    {dockDetails.map(dock => {
                        // Determine the display date for the dock card from dateTimeSlots
                        let displayDate = null;
                        if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
                            // Get all dates and find the earliest future date
                            const availableDates = Object.keys(dock.dateTimeSlots);
                            const futureDates = availableDates
                                .map(dateString => new Date(dateString + 'T12:00:00'))
                                .filter(date => isFutureDate(date))
                                .sort((a, b) => a.getTime() - b.getTime());

                            if (futureDates.length > 0) {
                                displayDate = futureDates[0];
                            }
                        }

                        return (
                            <DockCard
                                key={dock.id}
                                dock={{
                                    ...dock,
                                    imageUrl: dock.imageUrls?.[0] || dock.imageUrl || '',
                                    description: dock.description || '',
                                    availableDate: displayDate,
                                    price: dock.price?.toString() || '0',
                                    // Pass dateTimeSlots for enhanced display
                                    dateTimeSlots: dock.dateTimeSlots
                                }}
                                onDelete={deleteDockById}
                                onEdit={() => navigation.navigate('DockDetail' as any, { dockId: dock.id })}
                            />
                        );
                    })}
                </ScrollView>
            ) : (
                <View style={styles.emptyContainer}>
                    <Icon name="boat-outline" size={80} color="#ADB5BD" />
                    <Text style={styles.emptyTitle}>No Docks Yet</Text>
                    <Text style={styles.emptySubtitle}>
                        Start earning by listing your first dock. It only takes a few minutes!
                    </Text>
                    <TouchableOpacity
                        style={styles.addFirstDockButton}
                        onPress={() => navigation.navigate(Paths.DockDetail)}
                    >
                        <Icon name="add" size={20} color="white" />
                        <Text style={styles.addFirstDockText}>Add Your First Dock</Text>
                    </TouchableOpacity>
                </View>
            )}

            {/* Floating Action Button */}
            {dockDetails.length > 0 && (
                <TouchableOpacity
                    style={styles.fab}
                    onPress={() => navigation.navigate(Paths.DockDetail)}
                >
                    <Icon name="add" size={24} color="white" />
                </TouchableOpacity>
            )}
        </View>
        // </SafeScreen>
    );
}

const styles = StyleSheet.create({
    headerSection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingTop: 20,
        paddingBottom: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F1F3F4',
    },
    welcomeContainer: {
        marginBottom: 16,
    },
    welcomeText: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 4,
    },
    subtitleText: {
        fontSize: 16,
        color: '#6C757D',
        lineHeight: 22,
    },
    statsContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    statItem: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statText: {
        fontSize: 14,
        color: '#3674B5',
        fontWeight: '600',
        marginLeft: 6,
    },
    refreshButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: '#E3F2FD',
    },
    docksContainer: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 40,
        backgroundColor: '#F8F9FA',
    },
    emptyTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginTop: 24,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: 22,
        marginBottom: 32,
    },
    addFirstDockButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#3674B5',
        paddingHorizontal: 24,
        paddingVertical: 16,
        borderRadius: 12,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 4,
    },
    addFirstDockText: {
        color: 'white',
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8,
    },
    fab: {
        position: 'absolute',
        bottom: 24,
        right: 24,
        width: 56,
        height: 56,
        borderRadius: 28,
        backgroundColor: '#3674B5',
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 8,
    },
});

export default OwnerDashboard;
