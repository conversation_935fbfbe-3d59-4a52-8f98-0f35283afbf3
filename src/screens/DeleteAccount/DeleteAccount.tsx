import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, Alert, ScrollView, ActivityIndicator } from 'react-native';
import { TextInput } from 'react-native-paper';
import { getAuth, EmailAuthProvider, reauthenticateWithCredential, deleteUser } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Icon from 'react-native-vector-icons/Ionicons';
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';

function DeleteAccount({ navigation }: RootScreenProps<Paths.DeleteAccount>) {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [confirmationText, setConfirmationText] = useState('');
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [step, setStep] = useState(1); // 1: Warning, 2: Confirmation

    useEffect(() => {
        const auth = getAuth();
        const user = auth.currentUser;
        if (user?.email) {
            setEmail(user.email);
        }
    }, []);

    const handleProceedToConfirmation = () => {
        setStep(2);
    };

    const handleDeleteAccount = async () => {
        // Validation
        if (!password.trim()) {
            Alert.alert('Validation Error', 'Please enter your password to confirm deletion');
            return;
        }

        if (confirmationText.toLowerCase() !== 'delete my account') {
            Alert.alert('Validation Error', 'Please type "DELETE MY ACCOUNT" exactly to confirm');
            return;
        }

        const auth = getAuth();
        const user = auth.currentUser;

        if (!user || !user.email) {
            Alert.alert('Error', 'User not authenticated');
            return;
        }

        // Final confirmation
        Alert.alert(
            'Final Confirmation',
            'This action cannot be undone. Your account and all associated data will be permanently deleted.',
            [
                {
                    text: 'Cancel',
                    style: 'cancel',
                },
                {
                    text: 'Delete Forever',
                    style: 'destructive',
                    onPress: async () => {
                        setLoading(true);

                        try {
                            const credential = EmailAuthProvider.credential(user.email!, password);
                            await reauthenticateWithCredential(user, credential);
                            await deleteUser(user);

                            // Clear all user data from AsyncStorage
                            await AsyncStorage.multiRemove([
                                'currentUser',
                                'myUser',
                                'userRole'
                            ]);

                            Alert.alert(
                                'Account Deleted',
                                'Your account has been permanently deleted.',
                                [{ text: 'OK' }]
                            );

                            // The AuthProvider will automatically handle navigation when user is deleted
                        } catch (error: any) {
                            console.error('Account deletion error:', error);
                            let errorMessage = 'Failed to delete account';

                            if (error.code === 'auth/wrong-password') {
                                errorMessage = 'Incorrect password. Please try again.';
                            } else if (error.code === 'auth/requires-recent-login') {
                                errorMessage = 'Please log out and log back in before deleting your account';
                            }

                            Alert.alert('Error', errorMessage);
                        } finally {
                            setLoading(false);
                        }
                    }
                }
            ]
        );
    };

    if (step === 1) {
        return (
            <SafeAreaView style={styles.safeArea}>
                <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                    {/* Warning Header */}
                    <View style={styles.warningHeader}>
                        <View style={styles.warningIconContainer}>
                            <Icon name="warning" size={50} color="#DC3545" />
                        </View>
                        <Text style={styles.warningTitle}>Delete Account</Text>
                        <Text style={styles.warningSubtitle}>
                            This action cannot be undone
                        </Text>
                    </View>

                    {/* Warning Content */}
                    <View style={styles.warningContainer}>
                        <Text style={styles.warningText}>
                            Deleting your account will permanently remove:
                        </Text>

                        <View style={styles.consequencesList}>
                            <View style={styles.consequenceItem}>
                                <Icon name="person-remove" size={20} color="#DC3545" />
                                <Text style={styles.consequenceText}>Your profile and personal information</Text>
                            </View>
                            <View style={styles.consequenceItem}>
                                <Icon name="boat" size={20} color="#DC3545" />
                                <Text style={styles.consequenceText}>All your dock listings and bookings</Text>
                            </View>
                            <View style={styles.consequenceItem}>
                                <Icon name="chatbubbles" size={20} color="#DC3545" />
                                <Text style={styles.consequenceText}>Reviews and ratings</Text>
                            </View>
                            <View style={styles.consequenceItem}>
                                <Icon name="card" size={20} color="#DC3545" />
                                <Text style={styles.consequenceText}>Payment methods and transaction history</Text>
                            </View>
                            <View style={styles.consequenceItem}>
                                <Icon name="settings" size={20} color="#DC3545" />
                                <Text style={styles.consequenceText}>All account settings and preferences</Text>
                            </View>
                        </View>

                        <View style={styles.alternativeContainer}>
                            <Icon name="information-circle" size={24} color="#3674B5" />
                            <View style={styles.alternativeContent}>
                                <Text style={styles.alternativeTitle}>Consider these alternatives:</Text>
                                <Text style={styles.alternativeText}>
                                    • Contact support for assistance
                                </Text>
                            </View>
                        </View>
                    </View>

                    {/* Action Buttons */}
                    <View style={styles.actionContainer}>
                        <TouchableOpacity
                            style={styles.cancelButton}
                            onPress={() => navigation.goBack()}
                        >
                            <Icon name="arrow-back" size={20} color="#6C757D" />
                            <Text style={styles.cancelButtonText}>Keep My Account</Text>
                        </TouchableOpacity>

                        <TouchableOpacity
                            style={styles.proceedButton}
                            onPress={handleProceedToConfirmation}
                        >
                            <Icon name="arrow-forward" size={20} color="white" />
                            <Text style={styles.proceedButtonText}>Continue to Delete</Text>
                        </TouchableOpacity>
                    </View>
                </ScrollView>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                {/* Confirmation Header */}
                <View style={styles.confirmationHeader}>

                    <View style={styles.confirmationIconContainer}>
                        <Icon name="shield-checkmark" size={40} color="#DC3545" />
                    </View>
                    <Text style={styles.confirmationTitle}>Confirm Account Deletion</Text>
                    <Text style={styles.confirmationSubtitle}>
                        Please verify your identity to proceed
                    </Text>
                </View>

                {/* Confirmation Form */}
                <View style={styles.formContainer}>
                    {/* Email Display */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Account Email</Text>
                        <View style={styles.emailDisplay}>
                            <Icon name="mail" size={20} color="#6C757D" />
                            <Text style={styles.emailText}>{email}</Text>
                        </View>
                    </View>

                    {/* Password Input */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Confirm Password</Text>
                        <View style={styles.passwordInputContainer}>
                            <TextInput
                                value={password}
                                onChangeText={setPassword}
                                style={styles.textInput}
                                mode="outlined"
                                secureTextEntry={!showPassword}
                                activeOutlineColor="#DC3545"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                placeholder="Enter your password"
                                autoCapitalize="none"
                            />
                            <TouchableOpacity
                                style={styles.eyeButton}
                                onPress={() => setShowPassword(!showPassword)}
                            >
                                <Icon
                                    name={showPassword ? 'eye-off' : 'eye'}
                                    size={20}
                                    color="#6C757D"
                                />
                            </TouchableOpacity>
                        </View>
                    </View>

                    {/* Confirmation Text */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Type "DELETE MY ACCOUNT" to confirm</Text>
                        <TextInput
                            value={confirmationText}
                            onChangeText={setConfirmationText}
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#DC3545"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            placeholder="DELETE MY ACCOUNT"
                            autoCapitalize="characters"
                        />
                        <Text style={styles.helperText}>
                            This confirms you understand the consequences
                        </Text>
                    </View>

                    {/* Delete Button */}
                    <TouchableOpacity
                        style={[styles.deleteButton, loading && styles.deleteButtonDisabled]}
                        onPress={handleDeleteAccount}
                        disabled={loading || !password.trim() || confirmationText.toLowerCase() !== 'delete my account'}
                        activeOpacity={0.8}
                    >
                        {loading ? (
                            <ActivityIndicator size="small" color="white" />
                        ) : (
                            <Icon name="trash" size={20} color="white" />
                        )}
                        <Text style={styles.deleteButtonText}>
                            {loading ? 'Deleting Account...' : 'Delete My Account Forever'}
                        </Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    container: {
        flex: 1,
        paddingHorizontal: 20,
    },
    // Warning Step Styles
    warningHeader: {
        alignItems: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    warningIconContainer: {
        width: 100,
        height: 100,
        borderRadius: 50,
        backgroundColor: '#FFEBEE',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 20,
        borderWidth: 3,
        borderColor: '#FFCDD2',
    },
    warningTitle: {
        fontSize: 28,
        fontWeight: 'bold',
        color: '#DC3545',
        marginBottom: 8,
        textAlign: 'center',
    },
    warningSubtitle: {
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: 22,
    },
    warningContainer: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 24,
        marginBottom: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
        borderLeftWidth: 4,
        borderLeftColor: '#DC3545',
    },
    warningText: {
        fontSize: 18,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 20,
    },
    consequencesList: {
        gap: 16,
        marginBottom: 24,
    },
    consequenceItem: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: 12,
    },
    consequenceText: {
        fontSize: 16,
        color: '#495057',
        flex: 1,
        lineHeight: 22,
    },
    alternativeContainer: {
        flexDirection: 'row',
        backgroundColor: '#E3F2FD',
        borderRadius: 12,
        padding: 16,
        gap: 12,
        borderLeftWidth: 4,
        borderLeftColor: '#3674B5',
    },
    alternativeContent: {
        flex: 1,
    },
    alternativeTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#3674B5',
        marginBottom: 8,
    },
    alternativeText: {
        fontSize: 14,
        color: '#495057',
        lineHeight: 20,
    },
    actionContainer: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 20,
    },
    cancelButton: {
        flex: 1,
        backgroundColor: 'white',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        borderWidth: 2,
        borderColor: '#DEE2E6',
        gap: 8,
    },
    cancelButtonText: {
        fontSize: 13,
        fontWeight: '600',
        color: '#6C757D',
    },
    proceedButton: {
        flex: 1,
        backgroundColor: '#DC3545',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        shadowColor: '#DC3545',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
        gap: 8,
    },
    proceedButtonText: {
        fontSize: 13,
        fontWeight: 'bold',
        color: 'white',
    },
    // Confirmation Step Styles
    confirmationHeader: {
        alignItems: 'center',
        paddingVertical: 30,
        paddingHorizontal: 20,
        position: 'relative',
    },
    backButton: {
        position: 'absolute',
        left: 20,
        top: 30,
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    confirmationIconContainer: {
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: '#FFEBEE',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 20,
        borderWidth: 2,
        borderColor: '#FFCDD2',
    },
    confirmationTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 8,
        textAlign: 'center',
    },
    confirmationSubtitle: {
        fontSize: 16,
        color: '#6C757D',
        textAlign: 'center',
        lineHeight: 22,
    },
    formContainer: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 24,
        marginBottom: 20,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    inputGroup: {
        marginBottom: 24,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 8,
    },
    emailDisplay: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        borderRadius: 12,
        padding: 16,
        gap: 12,
        borderWidth: 1,
        borderColor: '#E9ECEF',
    },
    emailText: {
        fontSize: 16,
        color: '#495057',
        flex: 1,
    },
    passwordInputContainer: {
        position: 'relative',
    },
    textInput: {
        backgroundColor: 'white',
        fontSize: 16,
    },
    inputContent: {
        fontSize: 16,
        color: '#2C3E50',
        paddingRight: 50,
    },
    eyeButton: {
        position: 'absolute',
        right: 12,
        top: 16,
        padding: 8,
    },
    helperText: {
        fontSize: 12,
        color: '#6C757D',
        marginTop: 4,
        fontStyle: 'italic',
    },
    deleteButton: {
        backgroundColor: '#DC3545',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        shadowColor: '#DC3545',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
        gap: 8,
    },
    deleteButtonDisabled: {
        backgroundColor: '#6C757D',
        shadowOpacity: 0.1,
    },
    deleteButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'white',
    },
});

export default DeleteAccount;
