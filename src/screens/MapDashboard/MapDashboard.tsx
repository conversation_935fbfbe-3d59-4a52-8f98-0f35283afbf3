
import React, { useState } from 'react';
import { LeafletView } from 'react-native-leaflet-view';
// import Icon from 'react-native-vector-icons/Ionicons';

const DEFAULT_LOCATION = {
    latitude: -23.5489,
    longitude: -46.6388
}

const MapDashboard: React.FC = () => {
    const [markers, setMarkers] = useState([
        {
            position: {
                lat: DEFAULT_LOCATION.latitude,
                lng: DEFAULT_LOCATION.longitude,
            },
            icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png", // Use local PNG image. Make sure this file exists.
            size: 20,
        }
    ]);

    // Handle messages from LeafletView (e.g., map touch/click)
    const handleMessageReceived = (message: any) => {
        console.log('Message received:', message);
        // Log all event names and payloads for debugging
        if (message?.event) {
            console.log('Event:', message.event);
        }
        if (message?.payload) {
            console.log('Payload:', message.payload);
        }
        // Try to handle any event with lat/lng in payload
        if (message?.payload?.lat && message?.payload?.lng) {
            setMarkers([
                {
                    position: { lat: message.payload.lat, lng: message.payload.lng },
                    icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                    size: 20,
                }
            ]);
        }
    };

    return (
        <LeafletView
            mapCenterPosition={{
                lat: DEFAULT_LOCATION.latitude,
                lng: DEFAULT_LOCATION.longitude,
            }}
            onMessageReceived={handleMessageReceived}

            mapMarkers={markers}
        />
    );
}

export default MapDashboard;