

import React, { useEffect, useState } from 'react';
import { Alert, ActivityIndicator, View, Text } from 'react-native';
import { LeafletView } from 'react-native-leaflet-view';
import firestore from '@react-native-firebase/firestore';

const MapDashboard: React.FC = () => {
    const [markers, setMarkers] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchMarkers = async () => {
            try {
                const snapshot = await firestore().collection('docksList').get();
                const docs = snapshot.docs.map((doc, idx) => {
                    const data = doc.data();
                    // Parse lat/lng from Location string
                    let lat = 0, lng = 0;
                    if (typeof data.Location === 'string') {
                        const [latStr, lngStr] = data.Location.split(',');
                        lat = parseFloat(latStr);
                        lng = parseFloat(lngStr);
                    }
                    return {
                        position: { lat, lng },
                        icon: '📍',
                        size: [30, 30],
                        title: data.Name || '',
                        description: data.Type || '',
                        id: doc.id,
                    };
                });
                setMarkers(docs);
            } catch (e: any) {
                setError(e.message || 'Failed to fetch markers');
            } finally {
                setLoading(false);
            }
        };
        fetchMarkers();
    }, []);

    const handleMessageReceived = (message: any) => {
        if (message?.event === 'onMapMarkerClicked' && typeof message?.payload?.mapMarkerID === 'number') {
            const marker = markers[message.payload.mapMarkerID];
            if (marker) {
                Alert.alert(marker.title, marker.description);
            }
        }
    };

    if (loading) {
        return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><ActivityIndicator size="large" /></View>;
    }
    if (error) {
        return <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><Text style={{ color: 'red', fontSize: 16 }}>{error}</Text></View>;
    }

    return (
        <LeafletView
            mapCenterPosition={markers[0]?.position || { lat: 0, lng: 0 }}
            mapMarkers={markers}
            onMessageReceived={handleMessageReceived}
            zoom={7}
        />
    );
};

export default MapDashboard;