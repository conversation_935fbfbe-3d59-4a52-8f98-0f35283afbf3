
import React, { useEffect, useState } from 'react';
import { Alert, ActivityIndicator, View, Text, StyleSheet, SafeAreaView, TouchableOpacity } from 'react-native';
import { LeafletView } from 'react-native-leaflet-view';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';
import Icon from 'react-native-vector-icons/Ionicons';

const MapDashboard: React.FC = () => {
    const [markers, setMarkers] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchDocks = async () => {
            try {
                console.log('Fetching docks from Firebase...');

                // Fetch from 'docks' collection (the correct collection name)
                const docksCollection = collection(db, 'docksList');
                const snapshot = await getDocs(docksCollection);

                console.log('Found', snapshot.docs.length, 'docksList');

                const dockMarkers = snapshot.docs.map((doc) => {
                    const data = doc.data();
                    console.log('Processing dock:', JSON.stringify(data));

                    // Extract coordinates from Location field (format: "lat,lng")
                    let lat = 0, lng = 0;

                    if (data.Location && typeof data.Location === 'string') {
                        // Parse "25.00,-80.70" format
                        const [latStr, lngStr] = data.Location.split(',');
                        lat = parseFloat(latStr.trim()) || 0;
                        lng = parseFloat(lngStr.trim()) || 0;
                        console.log(`Parsed coordinates: ${lat}, ${lng} from "${data.Location}"`);
                    } else {
                        // Fallback: use default coordinates if no location data
                        lat = 25.7617; // Default Miami coordinates (closer to Florida Bay)
                        lng = -80.1918;
                        console.log('Using default Miami coordinates');
                    }

                    return {
                        position: { lat, lng },
                        icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                        size: [40, 40],
                        title: data.Name || 'Unnamed Location',
                        description: `${data.Type || 'Unknown'} - ${data.Region || 'Unknown Region'}`,
                        id: doc.id,
                        dockData: data, // Store full dock data for later use
                    };
                });

                setMarkers(dockMarkers);

            } catch (e: any) {
                console.error('Error fetching docks:', e);
                setError(e.message || 'Failed to fetch dock data');
            } finally {
                setLoading(false);
            }
        };

        fetchDocks();
    }, []);

    const handleMessageReceived = (message: any) => {

        // Handle marker clicks
        if (message?.event === 'onMapMarkerClicked' && typeof message?.payload?.mapMarkerID === 'number') {
            const marker = markers[message.payload.mapMarkerID];
            if (marker) {
                const dockData = marker.dockData;
                Alert.alert(
                    marker.title,
                    `${marker.description}\n\nLocation: ${dockData?.Location || 'N/A'}\nNatural/Man-made: ${dockData?.['Natural/Man-made'] || 'N/A'}\nCreated: ${dockData?.createdAt ? new Date(dockData.createdAt.seconds * 1000).toLocaleDateString() : 'N/A'}`,
                    [
                        { text: 'Close', style: 'cancel' },
                        { text: 'View Details', onPress: () => console.log('Navigate to location details:', marker.id) }
                    ]
                );
            }
        }
    };

    const refreshData = () => {
        setLoading(true);
        setError(null);
        // Re-trigger the useEffect by updating a dependency
        const fetchDocks = async () => {
            try {
                const docksCollection = collection(db, 'docksList');
                const snapshot = await getDocs(docksCollection);

                const dockMarkers = snapshot.docs.map((doc) => {
                    const data = doc.data();
                    console.log("doc", doc)
                    let lat = 0, lng = 0;

                    if (data.Location && typeof data.Location === 'string') {
                        // Parse "25.00,-80.70" format
                        const [latStr, lngStr] = data.Location.split(',');
                        lat = parseFloat(latStr.trim()) || 0;
                        lng = parseFloat(lngStr.trim()) || 0;
                    } else {
                        // Default coordinates for Miami area
                        lat = 25.7617 + (Math.random() - 0.5) * 0.1; // Add small random offset
                        lng = -80.1918 + (Math.random() - 0.5) * 0.1;
                    }

                    return {
                        position: { lat, lng },
                        icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                        size: [40, 40],
                        title: data.Name || 'Unnamed Location',
                        description: `${data.Type || 'Unknown'} - ${data.Region || 'Unknown Region'}`,
                        id: doc.id,
                        dockData: data,
                    };
                });

                setMarkers(dockMarkers);
                console.log("DocksMarkers:", dockMarkers);

            } catch (e: any) {
                console.error('Error refreshing docks:', e);
                setError(e.message || 'Failed to refresh dock data');
            } finally {
                setLoading(false);
            }
        };

        fetchDocks();
    };

    if (loading) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.loadingText}>Loading dock locations...</Text>
                </View>
            </SafeAreaView>
        );
    }

    if (error) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.errorContainer}>
                    <Icon name="alert-circle-outline" size={48} color="#FF3B30" />
                    <Text style={styles.errorTitle}>Error Loading Docks</Text>
                    <Text style={styles.errorText}>{error}</Text>
                    <TouchableOpacity style={styles.retryButton} onPress={refreshData}>
                        <Icon name="refresh-outline" size={20} color="#fff" />
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <Text style={styles.title}>Dock Locations</Text>
                <View style={styles.controls}>
                    <TouchableOpacity style={styles.refreshButton} onPress={refreshData}>
                        <Icon name="refresh-outline" size={20} color="#007AFF" />
                    </TouchableOpacity>
                    <View style={styles.markerCount}>
                        <Icon name="location-outline" size={16} color="#666" />
                        <Text style={styles.markerCountText}>{markers.length} docks</Text>
                    </View>
                </View>
            </View>

            {/* Map */}
            <View style={styles.mapContainer}>
                <LeafletView
                    mapCenterPosition={markers[0]?.position || { lat: 25.0, lng: -80.7 }}
                    mapMarkers={markers}
                    onMessageReceived={handleMessageReceived}
                    zoom={8}
                />
            </View>

            {/* Instructions */}
            <View style={styles.instructions}>
                <Text style={styles.instructionText}>
                    <Icon name="information-circle-outline" size={16} color="#666" /> Tap on markers to view dock details
                </Text>
            </View>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    controls: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    refreshButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: '#f0f0f0',
    },
    markerCount: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        gap: 4,
    },
    markerCountText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#666',
    },
    mapContainer: {
        flex: 1,
    },
    instructions: {
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    instructionText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16,
    },
    loadingText: {
        fontSize: 16,
        color: '#666',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
        gap: 16,
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FF3B30',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        lineHeight: 22,
    },
    retryButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 25,
        gap: 8,
        marginTop: 8,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default MapDashboard;