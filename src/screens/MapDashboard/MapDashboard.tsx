
import React, { useState } from 'react';
import { View, TouchableOpacity, Text, StyleSheet, SafeAreaView } from 'react-native';
import { LeafletView } from 'react-native-leaflet-view';
import Icon from 'react-native-vector-icons/Ionicons';

const DEFAULT_LOCATION = {
    latitude: -23.5489,
    longitude: -46.6388
}

// Different marker icons for variety
const MARKER_ICONS = [
    "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
    "https://cdn-icons-png.flaticon.com/512/684/684908.png", // Red marker
    "https://cdn-icons-png.flaticon.com/512/684/684909.png", // Blue marker
    "https://cdn-icons-png.flaticon.com/512/684/684910.png", // Green marker
];

const MapDashboard: React.FC = () => {
    const [markers, setMarkers] = useState([
        {
            id: 'initial-marker',
            position: {
                lat: DEFAULT_LOCATION.latitude,
                lng: DEFAULT_LOCATION.longitude,
            },
            icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
            size: "30",
        }
    ]);

    // Handle messages from LeafletView (e.g., map touch/click)
    const handleMessageReceived = (message: any) => {
        console.log('Message received:', message);

        // Log all event names and payloads for debugging
        if (message?.event) {
            console.log('Event:', message.event);
        }
        if (message?.payload) {
            console.log('Payload:', message.payload);
        }

        // Handle map click/touch events to add new markers
        if (message?.payload?.lat && message?.payload?.lng) {
            // Use different marker icons for variety
            const randomIcon = MARKER_ICONS[Math.floor(Math.random() * MARKER_ICONS.length)];

            const newMarker = {
                id: `marker-${Date.now()}`, // Unique ID for each marker
                position: {
                    lat: message.payload.lat,
                    lng: message.payload.lng
                },
                icon: randomIcon,
                size: "30",
            };

            // Add new marker to existing markers array
            setMarkers(prevMarkers => [...prevMarkers, newMarker]);

            console.log('Added new marker at:', message.payload.lat, message.payload.lng);
        }
    };

    // Function to clear all markers except the initial one
    const clearMarkers = () => {
        setMarkers([
            {
                id: 'initial-marker',
                position: {
                    lat: DEFAULT_LOCATION.latitude,
                    lng: DEFAULT_LOCATION.longitude,
                },
                icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                size: "30",
            }
        ]);
        console.log('Cleared all markers');
    };

    return (
        <SafeAreaView style={styles.container}>
            {/* Header with controls */}
            <View style={styles.header}>
                <Text style={styles.title}>Map Dashboard</Text>
                <View style={styles.controls}>
                    <TouchableOpacity style={styles.controlButton} onPress={clearMarkers}>
                        <Icon name="trash-outline" size={20} color="#fff" />
                        <Text style={styles.controlButtonText}>Clear</Text>
                    </TouchableOpacity>
                    <View style={styles.markerCount}>
                        <Icon name="location-outline" size={16} color="#666" />
                        <Text style={styles.markerCountText}>{markers.length}</Text>
                    </View>
                </View>
            </View>

            {/* Map View */}
            <View style={styles.mapContainer}>
                <LeafletView
                    mapCenterPosition={{
                        lat: DEFAULT_LOCATION.latitude,
                        lng: DEFAULT_LOCATION.longitude,
                    }}
                    onMessageReceived={handleMessageReceived}
                    mapMarkers={markers}
                    zoom={10}
                />
            </View>

            {/* Instructions */}
            <View style={styles.instructions}>
                <Text style={styles.instructionText}>
                    <Icon name="hand-left-outline" size={16} color="#666" /> Tap anywhere on the map to add a marker
                </Text>
            </View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    controls: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    controlButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#ff4444',
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        gap: 4,
    },
    controlButtonText: {
        color: '#fff',
        fontSize: 12,
        fontWeight: '600',
    },
    markerCount: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        gap: 4,
    },
    markerCountText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#666',
    },
    mapContainer: {
        flex: 1,
    },
    instructions: {
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    instructionText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
    },
});

export default MapDashboard;