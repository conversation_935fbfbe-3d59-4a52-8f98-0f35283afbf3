
import React, { useEffect, useState, useRef } from 'react';
import {
    Alert,
    ActivityIndicator,
    View,
    Text,
    StyleSheet,
    SafeAreaView,
    TouchableOpacity,
    Modal,
    Animated,
    PanResponder,
    Dimensions,
    Image
} from 'react-native';
import { LeafletView } from 'react-native-leaflet-view';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/config/firebase';
import Icon from 'react-native-vector-icons/Ionicons';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const BOTTOM_SHEET_MAX_HEIGHT = SCREEN_HEIGHT * 0.6;
const BOTTOM_SHEET_MIN_HEIGHT = 120;

const MapDashboard: React.FC = () => {
    const [markers, setMarkers] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedDock, setSelectedDock] = useState<any>(null);
    const [bottomSheetVisible, setBottomSheetVisible] = useState(false);

    const bottomSheetHeight = useRef(new Animated.Value(0)).current;

    // Bottom sheet animation functions
    const showBottomSheet = (dockData: any) => {
        setSelectedDock(dockData);
        setBottomSheetVisible(true);
        Animated.spring(bottomSheetHeight, {
            toValue: BOTTOM_SHEET_MIN_HEIGHT,
            useNativeDriver: false,
        }).start();
    };

    const hideBottomSheet = () => {
        Animated.spring(bottomSheetHeight, {
            toValue: 0,
            useNativeDriver: false,
        }).start(() => {
            setBottomSheetVisible(false);
            setSelectedDock(null);
        });
    };

    const expandBottomSheet = () => {
        Animated.spring(bottomSheetHeight, {
            toValue: BOTTOM_SHEET_MAX_HEIGHT,
            useNativeDriver: false,
        }).start();
    };

    useEffect(() => {
        const fetchDocks = async () => {
            try {
                console.log('Fetching docks from Firebase...');

                // Fetch from 'docks' collection (the correct collection name)
                const docksCollection = collection(db, 'docksList');
                const snapshot = await getDocs(docksCollection);

                console.log('Found', snapshot.docs.length, 'docksList');

                const dockMarkers = snapshot.docs.map((doc) => {
                    const data = doc.data();
                    console.log('Processing dock:', JSON.stringify(data));

                    // Extract coordinates from Location field (format: "lat,lng")
                    let lat = 0, lng = 0;

                    if (data.Location && typeof data.Location === 'string') {
                        // Parse "25.00,-80.70" format
                        const [latStr, lngStr] = data.Location.split(',');
                        lat = parseFloat(latStr.trim()) || 0;
                        lng = parseFloat(lngStr.trim()) || 0;
                        console.log(`Parsed coordinates: ${lat}, ${lng} from "${data.Location}"`);
                    } else {
                        // Fallback: use default coordinates if no location data
                        lat = 25.7617; // Default Miami coordinates (closer to Florida Bay)
                        lng = -80.1918;
                        console.log('Using default Miami coordinates');
                    }

                    return {
                        position: { lat, lng },
                        icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                        size: [40, 40],
                        title: data.Name || 'Unnamed Location',
                        description: `${data.Type || 'Unknown'} - ${data.Region || 'Unknown Region'}`,
                        id: doc.id,
                        dockData: data, // Store full dock data for later use
                    };
                });

                setMarkers(dockMarkers);

            } catch (e: any) {
                console.error('Error fetching docks:', e);
                setError(e.message || 'Failed to fetch dock data');
            } finally {
                setLoading(false);
            }
        };

        fetchDocks();
    }, []);

    const handleMessageReceived = (message: any) => {
        console.log('Map message received:', message);

        // Handle marker clicks
        if (message?.event === 'onMapMarkerClicked' && typeof message?.payload?.mapMarkerID === 'number') {
            const marker = markers[message.payload.mapMarkerID];
            if (marker) {
                console.log('Showing bottom sheet for:', marker.title);
                showBottomSheet(marker);
            }
        }
    };

    const refreshData = () => {
        setLoading(true);
        setError(null);
        // Re-trigger the useEffect by updating a dependency
        const fetchDocks = async () => {
            try {
                const docksCollection = collection(db, 'docksList');
                const snapshot = await getDocs(docksCollection);

                const dockMarkers = snapshot.docs.map((doc) => {
                    const data = doc.data();
                    console.log("doc", doc)
                    let lat = 0, lng = 0;

                    if (data.Location && typeof data.Location === 'string') {
                        // Parse "25.00,-80.70" format
                        const [latStr, lngStr] = data.Location.split(',');
                        lat = parseFloat(latStr.trim()) || 0;
                        lng = parseFloat(lngStr.trim()) || 0;
                    } else {
                        // Default coordinates for Miami area
                        lat = 25.7617 + (Math.random() - 0.5) * 0.1; // Add small random offset
                        lng = -80.1918 + (Math.random() - 0.5) * 0.1;
                    }

                    return {
                        position: { lat, lng },
                        icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                        size: [40, 40],
                        title: data.Name || 'Unnamed Location',
                        description: `${data.Type || 'Unknown'} - ${data.Region || 'Unknown Region'}`,
                        id: doc.id,
                        dockData: data,
                    };
                });

                setMarkers(dockMarkers);
                console.log("DocksMarkers:", dockMarkers);

            } catch (e: any) {
                console.error('Error refreshing docks:', e);
                setError(e.message || 'Failed to refresh dock data');
            } finally {
                setLoading(false);
            }
        };

        fetchDocks();
    };

    if (loading) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color="#007AFF" />
                    <Text style={styles.loadingText}>Loading dock locations...</Text>
                </View>
            </SafeAreaView>
        );
    }

    if (error) {
        return (
            <SafeAreaView style={styles.container}>
                <View style={styles.errorContainer}>
                    <Icon name="alert-circle-outline" size={48} color="#FF3B30" />
                    <Text style={styles.errorTitle}>Error Loading Docks</Text>
                    <Text style={styles.errorText}>{error}</Text>
                    <TouchableOpacity style={styles.retryButton} onPress={refreshData}>
                        <Icon name="refresh-outline" size={20} color="#fff" />
                        <Text style={styles.retryButtonText}>Retry</Text>
                    </TouchableOpacity>
                </View>
            </SafeAreaView>
        );
    }

    return (
        <SafeAreaView style={styles.container}>
            {/* Header */}
            <View style={styles.header}>
                <Text style={styles.title}>Dock Locations</Text>
                <View style={styles.controls}>
                    <TouchableOpacity style={styles.refreshButton} onPress={refreshData}>
                        <Icon name="refresh-outline" size={20} color="#007AFF" />
                    </TouchableOpacity>
                    <View style={styles.markerCount}>
                        <Icon name="location-outline" size={16} color="#666" />
                        <Text style={styles.markerCountText}>{markers.length} docks</Text>
                    </View>
                </View>
            </View>

            {/* Map */}
            <View style={styles.mapContainer}>
                <LeafletView
                    mapCenterPosition={markers[0]?.position || { lat: 25.0, lng: -80.7 }}
                    mapMarkers={markers}
                    onMessageReceived={handleMessageReceived}
                    zoom={8}
                />
            </View>

            {/* Instructions */}
            <View style={styles.instructions}>
                <Text style={styles.instructionText}>
                    <Icon name="information-circle-outline" size={16} color="#666" /> Tap on markers to view dock details
                </Text>
            </View>

            {/* Bottom Sheet Modal */}
            {bottomSheetVisible && (
                <Animated.View
                    style={[
                        styles.bottomSheet,
                        {
                            height: bottomSheetHeight,
                        }
                    ]}
                >
                    {/* Handle Bar */}
                    <View style={styles.handleBar} />

                    {/* Bottom Sheet Content */}
                    <View style={styles.bottomSheetContent}>
                        {selectedDock && (
                            <>
                                {/* Dock Details Header */}
                                <View style={styles.bottomSheetHeader}>
                                    <Text style={styles.bottomSheetTitle}>Dock Details</Text>
                                    <TouchableOpacity onPress={hideBottomSheet} style={styles.closeButton}>
                                        <Icon name="close" size={24} color="#666" />
                                    </TouchableOpacity>
                                </View>

                                {/* Dock Image */}
                                <View style={styles.imageContainer}>
                                    <Image
                                        source={{
                                            uri: selectedDock.dockData?.imageUrls?.[0] ||
                                                'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80'
                                        }}
                                        style={styles.dockImage}
                                        resizeMode="cover"
                                    />
                                </View>

                                {/* Dock Info */}
                                <View style={styles.dockInfo}>
                                    <View style={styles.dockNameRow}>
                                        <Text style={styles.dockName}>{selectedDock.title}</Text>
                                        <View style={styles.actionIcons}>
                                            <TouchableOpacity style={styles.iconButton}>
                                                <Icon name="heart-outline" size={20} color="#666" />
                                            </TouchableOpacity>
                                            <TouchableOpacity style={styles.iconButton}>
                                                <Icon name="chatbubble-outline" size={20} color="#666" />
                                            </TouchableOpacity>
                                        </View>
                                    </View>

                                    <Text style={styles.dockDescription}>
                                        {selectedDock.dockData?.Location || 'Location not specified'}
                                    </Text>

                                    {/* Price and View Details */}
                                    <View style={styles.bottomRow}>
                                        <View style={styles.priceContainer}>
                                            <Text style={styles.price}>
                                                ${selectedDock.dockData?.price || '2.22'}
                                            </Text>
                                            <Text style={styles.priceUnit}>/day</Text>
                                        </View>

                                        <TouchableOpacity
                                            style={styles.viewDetailsButton}
                                            onPress={expandBottomSheet}
                                        >
                                            <Text style={styles.viewDetailsText}>View Details</Text>
                                        </TouchableOpacity>
                                    </View>
                                </View>
                            </>
                        )}
                    </View>
                </Animated.View>
            )}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f5f5f5',
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 12,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    controls: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 12,
    },
    refreshButton: {
        padding: 8,
        borderRadius: 20,
        backgroundColor: '#f0f0f0',
    },
    markerCount: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#f0f0f0',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        gap: 4,
    },
    markerCountText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#666',
    },
    mapContainer: {
        flex: 1,
    },
    instructions: {
        backgroundColor: '#fff',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
    },
    instructionText: {
        fontSize: 14,
        color: '#666',
        textAlign: 'center',
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 16,
    },
    loadingText: {
        fontSize: 16,
        color: '#666',
    },
    errorContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingHorizontal: 32,
        gap: 16,
    },
    errorTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#FF3B30',
        textAlign: 'center',
    },
    errorText: {
        fontSize: 16,
        color: '#666',
        textAlign: 'center',
        lineHeight: 22,
    },
    retryButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#007AFF',
        paddingHorizontal: 20,
        paddingVertical: 12,
        borderRadius: 25,
        gap: 8,
        marginTop: 8,
    },
    retryButtonText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
    // Bottom Sheet Styles
    bottomSheet: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: '#fff',
        borderTopLeftRadius: 20,
        borderTopRightRadius: 20,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: -2 },
        shadowOpacity: 0.25,
        shadowRadius: 10,
        elevation: 10,
    },
    handleBar: {
        width: 40,
        height: 4,
        backgroundColor: '#ddd',
        borderRadius: 2,
        alignSelf: 'center',
        marginTop: 8,
        marginBottom: 16,
    },
    bottomSheetContent: {
        flex: 1,
        paddingHorizontal: 20,
    },
    bottomSheetHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    bottomSheetTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#333',
    },
    closeButton: {
        padding: 4,
    },
    imageContainer: {
        marginBottom: 16,
    },
    dockImage: {
        width: '100%',
        height: 200,
        borderRadius: 12,
        backgroundColor: '#f0f0f0',
    },
    dockInfo: {
        flex: 1,
    },
    dockNameRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    dockName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        marginRight: 12,
    },
    actionIcons: {
        flexDirection: 'row',
        gap: 8,
    },
    iconButton: {
        padding: 8,
        backgroundColor: '#f5f5f5',
        borderRadius: 20,
    },
    dockDescription: {
        fontSize: 14,
        color: '#666',
        marginBottom: 20,
        lineHeight: 20,
    },
    bottomRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: 16,
        borderTopWidth: 1,
        borderTopColor: '#f0f0f0',
    },
    priceContainer: {
        flexDirection: 'row',
        alignItems: 'baseline',
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
        color: '#333',
    },
    priceUnit: {
        fontSize: 14,
        color: '#666',
        marginLeft: 4,
    },
    viewDetailsButton: {
        backgroundColor: '#007AFF',
        paddingHorizontal: 24,
        paddingVertical: 12,
        borderRadius: 25,
    },
    viewDetailsText: {
        color: '#fff',
        fontSize: 16,
        fontWeight: '600',
    },
});

export default MapDashboard;