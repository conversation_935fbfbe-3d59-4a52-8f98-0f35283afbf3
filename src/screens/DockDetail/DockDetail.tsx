import { ScrollView, Text, TouchableOpacity, View, Alert, ActivityIndicator, StyleSheet, Dimensions, SafeAreaView } from 'react-native';
import { Paths } from '@/navigation/paths';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native-paper';
import type { RootScreenProps } from '@/navigation/types';
import ImageGallery from '@/components/imagePicker'; // Make sure this path is correct
import DatePicker from 'react-native-date-picker';
import { useEffect, useState } from 'react';
import Icon from 'react-native-vector-icons/Ionicons';

const { width: screenWidth } = Dimensions.get('window');

// Firebase Imports
import { getFirestore, collection, addDoc, Timestamp, updateDoc } from 'firebase/firestore';
import { app, auth } from '@/config/firebase'; // Adjust the path if necessary
import { doc, getDoc } from 'firebase/firestore';
import { getStorage, ref, uploadBytesResumable, getDownloadURL } from 'firebase/storage';
import { LeafletView } from 'react-native-leaflet-view';
// Make sure app and auth are imported from your config
// Initialize Firestore
const db = getFirestore(app);

// Define the type for an image object received from ImageGallery
interface SelectedImage {
    uri: string; // Local URI
    firebaseUrl?: string; // Firebase Storage URL after upload
    fileName?: string;
    type?: string;
}

// Define the type for time slots
interface TimeSlot {
    id: string;
    startTime: Date;
    endTime: Date;
}

// Type for date-slot mapping
type DateSlotsMap = { [date: string]: TimeSlot[] };

function DockDetail({ navigation, route }: RootScreenProps<Paths.DockDetail>) {
    // const { layout, backgrounds } = useTheme();
    const dockId = route.params?.dockId; // May be undefined if adding
    const { t } = useTranslation();

    // --- Form States ---
    const [dockName, setDockName] = useState('');
    const [dockDepth, setDockDepth] = useState('');
    const [dockLength, setDockLength] = useState('');
    const [dockWidth, setDockWidth] = useState('');
    const [dockType, setDockType] = useState('');
    const [address, setAddress] = useState('');
    const [city, setCity] = useState('');
    const [state, setState] = useState('');
    const [zipCode, setZipCode] = useState('');
    const [description, setDescription] = useState('');
    const [price, setPrice] = useState('');

    // --- Multi-Date/Time Picker States ---
    const [selectedDates, setSelectedDates] = useState<string[]>([]);
    const [selectedDay, setSelectedDay] = useState<Date | null>(null); // For date picker
    const [openDayPicker, setOpenDayPicker] = useState(false);
    const [currentEditingDate, setCurrentEditingDate] = useState<string | null>(null);

    // --- Multiple Time Slots State (per date) ---
    const [dateTimeSlots, setDateTimeSlots] = useState<DateSlotsMap>({});
    const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]); // Keep for backward compatibility
    const [editingSlotId, setEditingSlotId] = useState<string | null>(null);
    const [tempStartTime, setTempStartTime] = useState<Date | null>(null);
    const [tempEndTime, setTempEndTime] = useState<Date | null>(null);
    const [openStartTimePicker, setOpenStartTimePicker] = useState(false);
    const [openEndTimePicker, setOpenEndTimePicker] = useState(false);

    // --- Image Gallery State (Managed here and passed to ImageGallery) ---
    const [images, setImages] = useState<SelectedImage[]>([]);

    // --- Loading State for Form Submission ---
    const [submitting, setSubmitting] = useState(false);

    // --- Helper Functions for Multi-Date Time Slots ---
    const generateSlotId = () => `slot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Format date for display and storage
    const formatDateForStorage = (date: Date) => {
        return date.toISOString().split('T')[0]; // YYYY-MM-DD format
    };

    const formatDateForDisplay = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            day: '2-digit',
            month: 'short'
        });
    };

    // Add a new date
    const addSelectedDate = () => {
        if (!selectedDay) {
            Alert.alert('No Date Selected', 'Please select a date first.');
            return;
        }

        const dateString = formatDateForStorage(selectedDay);

        if (selectedDates.includes(dateString)) {
            Alert.alert('Date Already Added', 'This date has already been added.');
            return;
        }

        setSelectedDates(prev => [...prev, dateString].sort());
        setDateTimeSlots(prev => ({
            ...prev,
            [dateString]: []
        }));
        setSelectedDay(null); // Reset the picker
    };

    // Remove a date and its time slots
    const removeDateAndSlots = (dateString: string) => {
        Alert.alert(
            'Remove Date',
            `Are you sure you want to remove ${new Date(dateString).toLocaleDateString()} and all its time slots?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Remove',
                    style: 'destructive',
                    onPress: () => {
                        setSelectedDates(prev => prev.filter(d => d !== dateString));
                        setDateTimeSlots(prev => {
                            const newSlots = { ...prev };
                            delete newSlots[dateString];
                            return newSlots;
                        });
                        if (currentEditingDate === dateString) {
                            setCurrentEditingDate(null);
                        }
                    }
                }
            ]
        );
    };

    const addNewTimeSlot = () => {
        if (!currentEditingDate) {
            Alert.alert('No Date Selected', 'Please select a date to add time slots to.');
            return;
        }

        if (!tempStartTime || !tempEndTime) {
            Alert.alert('Missing Time', 'Please select both start and end times.');
            return;
        }

        if (tempStartTime >= tempEndTime) {
            Alert.alert('Invalid Time', 'Start time must be before end time.');
            return;
        }

        // Check for overlapping time slots within the same date
        const currentDateSlots = dateTimeSlots[currentEditingDate] || [];
        const hasOverlap = currentDateSlots.some(slot => {
            if (editingSlotId && slot.id === editingSlotId) return false;
            return (
                (tempStartTime >= slot.startTime && tempStartTime < slot.endTime) ||
                (tempEndTime > slot.startTime && tempEndTime <= slot.endTime) ||
                (tempStartTime <= slot.startTime && tempEndTime >= slot.endTime)
            );
        });

        if (hasOverlap) {
            Alert.alert('Time Conflict', 'This time slot overlaps with an existing slot for this date.');
            return;
        }

        const newSlot: TimeSlot = {
            id: editingSlotId || generateSlotId(),
            startTime: tempStartTime,
            endTime: tempEndTime,
        };

        setDateTimeSlots(prev => {
            const currentSlots = prev[currentEditingDate] || [];
            let updatedSlots;

            if (editingSlotId) {
                // Update existing slot
                updatedSlots = currentSlots.map(slot =>
                    slot.id === editingSlotId ? newSlot : slot
                );
            } else {
                // Add new slot
                updatedSlots = [...currentSlots, newSlot].sort((a, b) => a.startTime.getTime() - b.startTime.getTime());
            }

            return {
                ...prev,
                [currentEditingDate]: updatedSlots
            };
        });

        // Reset editing state
        setEditingSlotId(null);
        setTempStartTime(null);
        setTempEndTime(null);
    };

    // Edit time slot
    const editTimeSlot = (slot: TimeSlot) => {
        setEditingSlotId(slot.id);
        setTempStartTime(slot.startTime);
        setTempEndTime(slot.endTime);
    };

    // Cancel editing
    const cancelEdit = () => {
        setEditingSlotId(null);
        setTempStartTime(null);
        setTempEndTime(null);
    };

    const deleteTimeSlot = (slotId: string, dateString: string) => {
        Alert.alert(
            'Delete Time Slot',
            'Are you sure you want to delete this time slot?',
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Delete',
                    style: 'destructive',
                    onPress: () => {
                        setDateTimeSlots(prev => ({
                            ...prev,
                            [dateString]: (prev[dateString] || []).filter(slot => slot.id !== slotId)
                        }));
                        if (editingSlotId === slotId) {
                            setEditingSlotId(null);
                            setTempStartTime(null);
                            setTempEndTime(null);
                        }
                    }
                }
            ]
        );
    };



    // Initialize storage using your Firebase app instance

    useEffect(() => {
        images.forEach(image => {
            if (image.uri) {
                uploadImageData(image)
                    .then(url => {
                        // You can now save 'url' to your database (e.g., Firestore)
                    })
                    .catch(error => {
                        console.error('Upload failed:', error);
                        // Show an error to the user
                    });
            }
        });
    }, [images]);
    const storage = getStorage(app); // <-- Add this line

    const DEFAULT_LOCATION = {
        latitude: 28.83,
        longitude: -81.30
    }


    const [markers, setMarkers] = useState([
        {
            position: {
                lat: DEFAULT_LOCATION.latitude,
                lng: DEFAULT_LOCATION.longitude,
            },
            icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
            size: [30, 30], // Use array format for size
        }
    ]);

    // Handle messages from LeafletView (e.g., map touch/click)
    const handleMessageReceived = (message: any) => {
        console.log("message ====>", message?.payload?.touchLatLng)
        // console.log('Message received:', message);

        // Log all event names and payloads for debugging
        if (message?.event) {
            // console.log('Event:', message.event);
        }
        if (message?.payload) {
            // console.log('Payload:', message.payload);
        }

        // Handle different types of map events
        const eventType = message?.event;
        const payload = message?.payload;

        // Handle map click/touch events (various event names possible)
        if (message?.payload?.touchLatLng &&
            (eventType === 'onMapClicked' ||
                eventType === 'onMapTouched' ||
                eventType === 'onClick' ||
                eventType === 'onTouch' ||
                eventType === 'mapClicked' ||
                eventType === 'click' ||
                !eventType)) { // Sometimes no event type is provided

            // Replace the marker position with new touch location
            console.log("PAYS ====> ")
            setMarkers([
                {
                    position: {
                        lat: message?.payload?.touchLatLng.lat,
                        lng: message?.payload?.touchLatLng.lng
                    },
                    icon: "https://png.pngtree.com/png-vector/20230413/ourmid/pngtree-3d-location-icon-clipart-in-transparent-background-vector-png-image_6704161.png",
                    size: [30, 30],
                }
            ]);

            // console.log('Moved marker to:', payload.lat, payload.lng);
        }
    };
    const MARKERS = [
        {
            position: {
                lat: -23.5489,
                lng: -46.6388,
            },
            icon: '📍',
            size: [30, 30],
            title: 'Static Marker 1',
            description: 'This is marker 1 at São Paulo.',
            id: "0"
        },
        {
            position: {
                lat: -23.946096014998382,
                lng: -46.8017578125,
            },
            icon: '📍',
            size: [30, 30],
            title: 'Static Marker 2',
            description: 'This is marker 2 at the coast.',
            id: "1"
        },
    ];

    const uploadImageData = async (imageData: any) => {
        try {
            const user = auth.currentUser;
            if (!user) {
                console.error('User not authenticated');
                throw new Error('User not authenticated for image upload.');
            }

            const response = await fetch(imageData.uri);
            const blob = await response.blob();
            // Sanitize filename to ensure it's safe for Firebase Storage paths
            const safeFileName = imageData.fileName.replace(/[#[\]*?]/g, '_').replace(/\s+/g, '_');

            // Use the initialized 'storage' constant here
            // const storageRef = ref(storage, `dock_images/${user.uid}/${Date.now()}_${safeFileName}`);
            const storageRef = storage().ref('black-t-shirt-sm.png');


            const uploadTask = uploadBytesResumable(storageRef, blob, { contentType: imageData.type });

            return new Promise((resolve, reject) => {
                uploadTask.on(
                    'state_changed',
                    null, // No function for progress updates
                    (error) => {
                        console.error('[Upload Error] Upload failed:', error);
                        if (error.code) {
                            console.error('[Upload Error Code]:', error.code);
                        }
                        if (error.serverResponse) {
                            console.error('[Upload Error Server Response]:', error.serverResponse);
                        }
                        reject(error);
                    },
                    () => {
                        getDownloadURL(uploadTask.snapshot.ref)
                            .then(downloadURL => {
                                console.log('[Upload Complete] Image uploaded. Download URL obtained.');
                                resolve(downloadURL);
                            })
                            .catch(err => {
                                console.error('[GetDownloadURL Error]:', err);
                                reject(err);
                            });
                    }
                );
            });
        } catch (error) {
            console.error('Error in uploadImageData (preparation/network):', error);
            throw error;
        }
    };


    useEffect(() => {
        const fetchDockForEdit = async () => {
            if (!dockId) return;

            try {
                const docRef = doc(db, 'docks', dockId);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const data = docSnap.data();

                    setDockName(data.name || '');
                    setDockDepth(data.depth?.toString() || '');
                    setDockLength(data.length?.toString() || '');
                    setDockWidth(data.width?.toString() || '');
                    setDockType(data.type?.toString() || '');
                    setSelectedDay(data.availableDate?.toDate?.() || null);

                    // Load multi-date time slots or convert from legacy format
                    if (data.dateTimeSlots && typeof data.dateTimeSlots === 'object') {
                        // New multi-date format
                        const loadedDateSlots: DateSlotsMap = {};
                        const allDates: string[] = [];

                        Object.entries(data.dateTimeSlots).forEach(([dateString, slots]: [string, any]) => {
                            if (Array.isArray(slots)) {
                                loadedDateSlots[dateString] = slots.map((slot: any, index: number) => ({
                                    id: slot.id || `slot_${dateString}_${index}`,
                                    startTime: slot.startTime?.toDate?.() || new Date(),
                                    endTime: slot.endTime?.toDate?.() || new Date(),
                                }));
                                allDates.push(dateString);
                            }
                        });

                        setDateTimeSlots(loadedDateSlots);
                        setSelectedDates(allDates.sort());
                    } else if (data.timeSlots && Array.isArray(data.timeSlots)) {
                        // Legacy single-date format - convert to multi-date
                        const loadedSlots = data.timeSlots.map((slot: any, index: number) => ({
                            id: slot.id || `legacy_${index}`,
                            startTime: slot.startTime?.toDate?.() || new Date(),
                            endTime: slot.endTime?.toDate?.() || new Date(),
                        }));

                        if (data.availableDate) {
                            const dateString = formatDateForStorage(data.availableDate.toDate());
                            setDateTimeSlots({ [dateString]: loadedSlots });
                            setSelectedDates([dateString]);
                        }

                        setTimeSlots(loadedSlots); // Keep for backward compatibility
                    } else if (data.startTime && data.endTime) {
                        // Legacy single slot format
                        const legacySlot: TimeSlot = {
                            id: 'legacy_slot',
                            startTime: data.startTime.toDate(),
                            endTime: data.endTime.toDate(),
                        };

                        if (data.availableDate) {
                            const dateString = formatDateForStorage(data.availableDate.toDate());
                            setDateTimeSlots({ [dateString]: [legacySlot] });
                            setSelectedDates([dateString]);
                        }

                        setTimeSlots([legacySlot]); // Keep for backward compatibility
                    }

                    setAddress(data.address || '');
                    setCity(data.city || '');
                    setState(data.state || '');
                    setZipCode(data.zipCode || '');
                    setDescription(data.description || '');
                    setPrice(data.price?.toString() || '');

                    if (data.imageUrls) {
                        setImages(data.imageUrls.map((url: string) => ({ uri: url, firebaseUrl: url })));
                    }
                }
            } catch (err) {
                console.error('Failed to fetch dock for editing:', err);
            }
        };

        fetchDockForEdit();
    }, [dockId]);

    // --- Form Submission Handler ---
    const handleAddDock = async () => {
        // 1. Basic Validation
        const totalTimeSlots = Object.values(dateTimeSlots).reduce((total, slots) => total + slots.length, 0);

        if (
            !dockName ||
            !dockDepth ||
            !dockLength ||
            !dockWidth ||
            !dockType ||
            selectedDates.length === 0 ||
            totalTimeSlots === 0 ||
            !address ||
            !city ||
            !state ||
            !zipCode ||
            !description ||
            !price
        ) {
            Alert.alert('Missing Information', 'Please fill in all required fields and add at least one date with time slots.');
            return;
        }

        const allImagesUploaded = images.every(img => img.firebaseUrl);
        // if (!allImagesUploaded) {
        //     Alert.alert('Image Upload Pending', 'Please wait for all images to finish uploading before submitting.');
        //     return;
        // }

        setSubmitting(true);

        try {
            const imageUrlsToSave = images.map(img => img.firebaseUrl).filter(Boolean);

            // Convert multi-date time slots to Firebase format
            const dateTimeSlotsData: { [key: string]: any[] } = {};
            Object.entries(dateTimeSlots).forEach(([dateString, slots]) => {
                dateTimeSlotsData[dateString] = slots.map(slot => ({
                    id: slot.id,
                    startTime: Timestamp.fromDate(slot.startTime),
                    endTime: Timestamp.fromDate(slot.endTime),
                }));
            });

            const dockData = {
                userId: auth?.currentUser?.uid,
                name: dockName,
                depth: parseFloat(dockDepth),
                length: parseFloat(dockLength),
                width: parseFloat(dockWidth),
                type: dockType,
                // Only use dateTimeSlots - no redundant fields
                dateTimeSlots: dateTimeSlotsData,
                address,
                city,
                state,
                zipCode,
                description,
                imageUrls: imageUrlsToSave,
                price: parseFloat(price) || 0,
            };

            console.log("=== SAVING DOCK DATA ===");
            console.log("dateTimeSlots:", dateTimeSlots);
            console.log("dateTimeSlotsData:", dateTimeSlotsData);
            console.log("Full dockData:", dockData);
            console.log("========================");

            if (dockId) {
                // 🔄 Edit Mode: Update existing document
                const docRef = doc(db, 'docks', dockId);
                await updateDoc(docRef, dockData);
            } else {
                // ➕ Create Mode: Add new document
                await addDoc(collection(db, 'docks'), {
                    ...dockData,
                    createdAt: Timestamp.now(),
                });
            }

            // Optionally navigate back or reset form
            navigation.goBack();
        } catch (e: any) {
            console.error('Error saving dock data:', e);
            Alert.alert('Submission Failed', `An error occurred: ${e.message || 'Please try again.'}`);
        } finally {
            setSubmitting(false);
        }
    };


    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView
                style={styles.container}
                contentContainerStyle={styles.contentContainer}
                keyboardShouldPersistTaps="handled"
                showsVerticalScrollIndicator={false}
            >


                {/* Image Gallery Section */}
                <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                        <Icon name="images" size={24} color="#3674B5" />
                        <Text style={styles.sectionTitle}>Dock Photos</Text>
                    </View>
                    <Text style={styles.sectionSubtitle}>
                        Add photos to showcase your dock
                    </Text>
                    <ImageGallery selectedImages={images} setSelectedImages={setImages} />
                </View>

                {/* Basic Information Section */}
                <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                        <Icon name="information-circle" size={24} color="#3674B5" />
                        <Text style={styles.sectionTitle}>Basic Information</Text>
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Dock Name</Text>
                        <TextInput
                            placeholder="Enter dock name"
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            value={dockName}
                            onChangeText={setDockName}
                        />
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Dock Type</Text>
                        <TextInput
                            placeholder="e.g., Floating, Fixed, etc."
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            value={dockType}
                            onChangeText={setDockType}
                        />
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Description</Text>
                        <TextInput
                            placeholder="Describe your dock and its features"
                            style={[styles.textInput, styles.textArea]}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            multiline
                            numberOfLines={4}
                            value={description}
                            onChangeText={setDescription}
                        />
                    </View>
                </View>

                {/* Specifications Section */}
                <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                        <Icon name="resize" size={24} color="#3674B5" />
                        <Text style={styles.sectionTitle}>Specifications</Text>
                    </View>

                    <View style={styles.specsGrid}>
                        <View style={styles.specItem}>
                            <Text style={styles.inputLabel}>Length (ft)</Text>
                            <TextInput
                                placeholder="0"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                keyboardType="numeric"
                                value={dockLength}
                                onChangeText={setDockLength}
                            />
                        </View>

                        <View style={styles.specItem}>
                            <Text style={styles.inputLabel}>Width (ft)</Text>
                            <TextInput
                                placeholder="0"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                keyboardType="numeric"
                                value={dockWidth}
                                onChangeText={setDockWidth}
                            />
                        </View>
                    </View>

                    <View style={styles.specsGrid}>
                        <View style={styles.specItem}>
                            <Text style={styles.inputLabel}>Depth (m)</Text>
                            <TextInput
                                placeholder="0"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                keyboardType="numeric"
                                value={dockDepth}
                                onChangeText={setDockDepth}
                            />
                        </View>

                        <View style={styles.specItem}>
                            <Text style={styles.inputLabel}>Price ($) / slot</Text>
                            <TextInput
                                placeholder="0"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                keyboardType="numeric"
                                value={price}
                                onChangeText={setPrice}
                            />
                        </View>
                    </View>
                </View>

                {/* Multi-Date Availability Section */}
                <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                        <Icon name="calendar" size={24} color="#3674B5" />
                        <Text style={styles.sectionTitle}>Multi-Date Availability</Text>
                    </View>

                    {/* Date Selection */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Add Available Dates</Text>
                        <TouchableOpacity
                            style={styles.datePickerButton}
                            onPress={() => setOpenDayPicker(true)}
                        >
                            <Icon name="calendar-outline" size={20} color="#3674B5" />
                            <Text style={styles.datePickerText}>
                                {selectedDay ? selectedDay.toLocaleDateString() : 'Select date to add'}
                            </Text>
                            <Icon name="chevron-down" size={20} color="#6C757D" />
                        </TouchableOpacity>

                        {selectedDay && (
                            <TouchableOpacity
                                style={styles.addDateButton}
                                onPress={addSelectedDate}
                            >
                                <Icon name="add" size={20} color="white" />
                                <Text style={styles.addDateButtonText}>Add Date</Text>
                            </TouchableOpacity>
                        )}
                    </View>

                    <DatePicker
                        mode="date"
                        modal
                        open={openDayPicker}
                        date={selectedDay ?? new Date()}
                        onConfirm={(date) => {
                            setOpenDayPicker(false);
                            setSelectedDay(date);
                        }}
                        onCancel={() => {
                            setOpenDayPicker(false);
                        }}
                    />

                    {/* Selected Dates List */}
                    {selectedDates.length > 0 && (
                        <View style={styles.selectedDatesContainer}>
                            <Text style={styles.subsectionTitle}>Selected Dates ({selectedDates.length})</Text>
                            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                                <View style={styles.selectedDatesList}>
                                    {selectedDates.map((dateString) => (
                                        <View key={dateString} style={styles.selectedDateChip}>
                                            <Text style={styles.selectedDateText}>
                                                {formatDateForDisplay(dateString)}
                                            </Text>
                                            <TouchableOpacity
                                                onPress={() => removeDateAndSlots(dateString)}
                                                style={styles.removeDateButton}
                                            >
                                                <Icon name="close" size={16} color="#DC3545" />
                                            </TouchableOpacity>
                                        </View>
                                    ))}
                                </View>
                            </ScrollView>
                        </View>
                    )}

                    {/* Multi-Date Time Slots Section */}
                    {selectedDates.length > 0 && (
                        <View style={styles.timeSlotsContainer}>
                            <Text style={styles.subsectionTitle}>Time Slots per Date</Text>
                            <Text style={styles.subsectionSubtitle}>
                                Select a date to add time slots
                            </Text>

                            {/* Date Tabs */}
                            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateTabsContainer}>
                                <View style={styles.dateTabsList}>
                                    {selectedDates.map((dateString) => (
                                        <TouchableOpacity
                                            key={dateString}
                                            style={[
                                                styles.dateTab,
                                                currentEditingDate === dateString && styles.dateTabActive
                                            ]}
                                            onPress={() => setCurrentEditingDate(dateString)}
                                        >
                                            <Text style={[
                                                styles.dateTabText,
                                                currentEditingDate === dateString && styles.dateTabTextActive
                                            ]}>
                                                {formatDateForDisplay(dateString)}
                                            </Text>
                                            <Text style={[
                                                styles.dateTabSlotCount,
                                                currentEditingDate === dateString && styles.dateTabSlotCountActive
                                            ]}>
                                                {(dateTimeSlots[dateString] || []).length} slots
                                            </Text>
                                        </TouchableOpacity>
                                    ))}
                                </View>
                            </ScrollView>

                            {/* Time Slots for Selected Date */}
                            {currentEditingDate && (
                                <View style={styles.currentDateSlotsContainer}>
                                    <Text style={styles.currentDateTitle}>
                                        Time Slots for {new Date(currentEditingDate).toLocaleDateString('en-US', {
                                            weekday: 'long',
                                            day: '2-digit',
                                            month: 'long',
                                            year: 'numeric'
                                        })}
                                    </Text>

                                    {/* Existing Time Slots for Current Date */}
                                    {(dateTimeSlots[currentEditingDate] || []).length === 0 ? (
                                        <View style={styles.emptyState}>
                                            <Icon name="time-outline" size={32} color="#ADB5BD" />
                                            <Text style={styles.emptyStateText}>
                                                No time slots added for this date
                                            </Text>
                                            <Text style={styles.emptyStateSubtext}>
                                                Add your first time slot below
                                            </Text>
                                        </View>
                                    ) : (
                                        <View style={styles.timeSlotsList}>
                                            {(dateTimeSlots[currentEditingDate] || []).map((slot) => (
                                                <View key={slot.id} style={styles.timeSlotCard}>
                                                    <View style={styles.timeSlotInfo}>
                                                        <Icon name="time-outline" size={20} color="#3674B5" />
                                                        <Text style={styles.timeSlotTime}>
                                                            {slot.startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {slot.endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                                        </Text>
                                                    </View>
                                                    <View style={styles.timeSlotActions}>
                                                        <TouchableOpacity
                                                            style={styles.editTimeSlotButton}
                                                            onPress={() => editTimeSlot(slot)}
                                                        >
                                                            <Icon name="pencil" size={16} color="#3674B5" />
                                                        </TouchableOpacity>
                                                        <TouchableOpacity
                                                            style={styles.deleteTimeSlotButton}
                                                            onPress={() => deleteTimeSlot(slot.id, currentEditingDate)}
                                                        >
                                                            <Icon name="trash" size={16} color="#DC3545" />
                                                        </TouchableOpacity>
                                                    </View>
                                                </View>
                                            ))}
                                        </View>
                                    )}
                                </View>
                            )}
                        </View>
                    )}

                    {/* Prompt to add dates if none selected */}
                    {selectedDates.length === 0 && (
                        <View style={styles.emptyDatesState}>
                            <Icon name="calendar-outline" size={48} color="#ADB5BD" />
                            <Text style={styles.emptyDatesText}>
                                No dates selected
                            </Text>
                            <Text style={styles.emptyDatesSubtext}>
                                Add available dates above to start adding time slots
                            </Text>
                        </View>
                    )}

                    {/* Add New Time Slot Form - Only show when a date is selected */}
                    {currentEditingDate && (
                        <View style={styles.addTimeSlotContainer}>
                            <Text style={styles.addTimeSlotTitle}>
                                {editingSlotId ? 'Edit Time Slot' : `Add Time Slot for ${formatDateForDisplay(currentEditingDate)}`}
                            </Text>

                            <View style={styles.timePickerRow}>
                                <TouchableOpacity
                                    style={styles.timePickerButton}
                                    onPress={() => setOpenStartTimePicker(true)}
                                >
                                    <Icon name="time-outline" size={20} color="#3674B5" />
                                    <Text style={styles.timePickerText}>
                                        {tempStartTime ? tempStartTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'Start Time'}
                                    </Text>
                                </TouchableOpacity>

                                <Text style={styles.timeSeparator}>to</Text>

                                <TouchableOpacity
                                    style={styles.timePickerButton}
                                    onPress={() => setOpenEndTimePicker(true)}
                                >
                                    <Icon name="time-outline" size={20} color="#3674B5" />
                                    <Text style={styles.timePickerText}>
                                        {tempEndTime ? tempEndTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : 'End Time'}
                                    </Text>
                                </TouchableOpacity>
                            </View>

                            <View style={styles.timeSlotActionButtons}>
                                {editingSlotId && (
                                    <TouchableOpacity
                                        style={styles.cancelTimeSlotButton}
                                        onPress={cancelEdit}
                                    >
                                        <Text style={styles.cancelTimeSlotButtonText}>Cancel</Text>
                                    </TouchableOpacity>
                                )}
                                <TouchableOpacity
                                    style={styles.addTimeSlotButton}
                                    onPress={addNewTimeSlot}
                                >
                                    <Icon name="add" size={20} color="white" />
                                    <Text style={styles.addTimeSlotButtonText}>
                                        {editingSlotId ? 'Update' : 'Add Slot'}
                                    </Text>
                                </TouchableOpacity>
                            </View>
                        </View>
                    )}
                </View>

                {/* Time Pickers */}
                <DatePicker
                    mode="time"
                    modal
                    open={openStartTimePicker}
                    date={tempStartTime ?? new Date()}
                    onConfirm={(date) => {
                        setOpenStartTimePicker(false);
                        // Combine selected time with selected date
                        if (selectedDay) {
                            // Create a new date using the selected date's year, month, day
                            // and the selected time's hours and minutes
                            const combinedDateTime = new Date(
                                selectedDay.getFullYear(),
                                selectedDay.getMonth(),
                                selectedDay.getDate(),
                                date.getHours(),
                                date.getMinutes(),
                                0, // seconds
                                0  // milliseconds
                            );
                            setTempStartTime(combinedDateTime);
                        } else {
                            setTempStartTime(date);
                        }
                    }}
                    onCancel={() => setOpenStartTimePicker(false)}
                    minuteInterval={15}
                />

                <DatePicker
                    mode="time"
                    modal
                    open={openEndTimePicker}
                    date={tempEndTime ?? new Date()}
                    onConfirm={(date) => {
                        setOpenEndTimePicker(false);
                        // Combine selected time with selected date
                        if (selectedDay) {
                            // Create a new date using the selected date's year, month, day
                            // and the selected time's hours and minutes
                            const combinedDateTime = new Date(
                                selectedDay.getFullYear(),
                                selectedDay.getMonth(),
                                selectedDay.getDate(),
                                date.getHours(),
                                date.getMinutes(),
                                0, // seconds
                                0  // milliseconds
                            );
                            setTempEndTime(combinedDateTime);
                        } else {
                            setTempEndTime(date);
                        }
                    }}
                    onCancel={() => setOpenEndTimePicker(false)}
                    minuteInterval={15}
                />

                {/* Location Section */}
                <View style={styles.section}>
                    <View style={styles.sectionHeader}>
                        <Icon name="location" size={24} color="#3674B5" />
                        <Text style={styles.sectionTitle}>Location</Text>
                    </View>

                    {/* <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Street Address</Text>
                        <TextInput
                            placeholder="Enter street address"
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            value={address}
                            onChangeText={setAddress}
                        />
                    </View>

                    <View style={styles.locationGrid}>
                        <View style={styles.locationItem}>
                            <Text style={styles.inputLabel}>City</Text>
                            <TextInput
                                placeholder="City"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                value={city}
                                onChangeText={setCity}
                            />
                        </View>

                        <View style={styles.locationItem}>
                            <Text style={styles.inputLabel}>State</Text>
                            <TextInput
                                placeholder="State"
                                style={styles.textInput}
                                mode="outlined"
                                activeOutlineColor="#3674B5"
                                outlineColor="#E9ECEF"
                                contentStyle={styles.inputContent}
                                value={state}
                                onChangeText={setState}
                            />
                        </View>
                    </View>

                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>ZIP Code</Text>
                        <TextInput
                            placeholder="Enter ZIP code"
                            style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            contentStyle={styles.inputContent}
                            keyboardType="numeric"
                            value={zipCode}
                            onChangeText={setZipCode}
                        />
                    </View> */}
                    <View style={styles.inputGroup}>
                        <Text style={styles.inputLabel}>Latitude & Longitude</Text>
                        <TextInput
                            placeholder="Latitude, Longitude"
                            // style={styles.textInput}
                            mode="outlined"
                            activeOutlineColor="#3674B5"
                            outlineColor="#E9ECEF"
                            // contentStyle={styles.inputContent}
                            keyboardType="numeric"
                            value={markers[0]?.position?.lat + ", " + markers[0]?.position?.lng}
                            onChangeText={setZipCode}
                            disabled
                            // numberOfLines={1}
                            multiline={true}
                            scrollEnabled={false}
                        />
                    </View>
                    <View style={{ height: 250 }}>
                        <LeafletView
                            // mapCenterPosition={{
                            //     lat: markers[0]?.position?.lat,
                            //     lng: DEFAULT_LOCATION.longitude,
                            // }}
                            onMessageReceived={handleMessageReceived}
                            mapMarkers={markers}
                            zoom={10}
                        />
                    </View>
                </View>

                {/* Submit Button */}
                <View style={styles.submitContainer}>
                    <TouchableOpacity
                        style={[styles.submitButton, submitting && styles.submitButtonDisabled]}
                        onPress={handleAddDock}
                        disabled={submitting}
                        activeOpacity={0.8}
                    >
                        {submitting ? (
                            <ActivityIndicator size="small" color="white" />
                        ) : (
                            <Icon name={dockId ? "checkmark" : "add"} size={20} color="white" />
                        )}
                        <Text style={styles.submitButtonText}>
                            {submitting
                                ? (dockId ? 'Updating...' : 'Adding...')
                                : (dockId ? 'Update Dock' : 'Add Dock')
                            }
                        </Text>
                    </TouchableOpacity>
                </View>
            </ScrollView>
        </SafeAreaView >
    );
}

const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    container: {
        flex: 1,
    },
    contentContainer: {
        paddingBottom: 20,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingTop: 50,
        paddingBottom: 15,
        backgroundColor: 'white',
        borderBottomWidth: 1,
        borderBottomColor: '#F1F3F4',
    },
    backButton: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#F8F9FA',
        justifyContent: 'center',
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
    },
    headerSpacer: {
        width: 40,
    },
    section: {
        backgroundColor: 'white',
        marginHorizontal: 20,
        marginTop: 20,
        borderRadius: 16,
        padding: 24,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    sectionHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 8,
        gap: 12,
    },
    sectionTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#2C3E50',
    },
    sectionSubtitle: {
        fontSize: 14,
        color: '#6C757D',
        marginBottom: 20,
        lineHeight: 20,
    },
    inputGroup: {
        marginBottom: 20,
    },
    inputLabel: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 8,
    },
    textInput: {
        backgroundColor: 'white',
        fontSize: 16,
    },
    inputContent: {
        fontSize: 16,
        color: '#2C3E50',
    },
    textArea: {
        minHeight: 100,
    },
    specsGrid: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 16,
    },
    specItem: {
        flex: 1,
    },
    datePickerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        backgroundColor: '#F8F9FA',
        borderRadius: 12,
        padding: 16,
        borderWidth: 1,
        borderColor: '#E9ECEF',
    },
    datePickerText: {
        fontSize: 16,
        color: '#2C3E50',
        flex: 1,
        marginLeft: 12,
    },
    // Time Slots Styles
    timeSlotsContainer: {
        marginTop: 20,
    },
    subsectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: 4,
    },
    subsectionSubtitle: {
        fontSize: 14,
        color: '#6C757D',
        marginBottom: 16,
    },
    timeSlotsList: {
        gap: 8,
        marginBottom: 16,
    },
    timeSlotCard: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        backgroundColor: '#F8F9FA',
        padding: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E9ECEF',
    },
    timeSlotInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    timeSlotTime: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginLeft: 8,
    },
    timeSlotActions: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
    },
    editTimeSlotButton: {
        padding: 8,
        backgroundColor: '#E3F2FD',
        borderRadius: 6,
    },
    deleteTimeSlotButton: {
        padding: 8,
        backgroundColor: '#FFEBEE',
        borderRadius: 6,
    },
    emptyState: {
        alignItems: 'center',
        paddingVertical: 24,
        paddingHorizontal: 16,
        backgroundColor: '#F8F9FA',
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E9ECEF',
        borderStyle: 'dashed',
        marginBottom: 16,
    },
    emptyStateText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#6C757D',
        marginTop: 8,
        textAlign: 'center',
    },
    emptyStateSubtext: {
        fontSize: 14,
        color: '#ADB5BD',
        marginTop: 4,
        textAlign: 'center',
    },
    addTimeSlotContainer: {
        backgroundColor: '#F8F9FA',
        padding: 16,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E9ECEF',
        marginTop: 8,
    },
    addTimeSlotTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 12,
        textAlign: 'center',
    },
    timePickerRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    timePickerButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'white',
        paddingHorizontal: 10,
        paddingVertical: 12,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: '#E9ECEF',
        flex: 0.42,
        gap: 8,
    },
    timePickerText: {
        fontSize: 13,
        color: '#495057',
        fontWeight: '500',
    },
    timeSeparator: {
        fontSize: 14,
        color: '#6C757D',
        fontWeight: '500',
    },
    timeSlotActionButtons: {
        flexDirection: 'row',
        justifyContent: 'flex-end',
        alignItems: 'center',
        gap: 12,
    },
    cancelTimeSlotButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 6,
        backgroundColor: 'white',
        borderWidth: 1,
        borderColor: '#DEE2E6',
    },
    cancelTimeSlotButtonText: {
        fontSize: 14,
        color: '#6C757D',
        fontWeight: '500',
    },
    addTimeSlotButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 6,
        backgroundColor: '#3674B5',
        gap: 4,
    },
    addTimeSlotButtonText: {
        fontSize: 14,
        color: 'white',
        fontWeight: '600',
    },
    // Location Styles
    locationGrid: {
        flexDirection: 'row',
        gap: 12,
        marginBottom: 16,
    },
    locationItem: {
        flex: 1,
    },
    // Submit Button Styles
    submitContainer: {
        marginHorizontal: 20,
        marginTop: 20,
        marginBottom: 20,
    },
    submitButton: {
        backgroundColor: '#3674B5',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 16,
        borderRadius: 12,
        shadowColor: '#3674B5',
        shadowOffset: {
            width: 0,
            height: 4,
        },
        shadowOpacity: 0.3,
        shadowRadius: 8,
        elevation: 6,
        gap: 8,
    },
    submitButtonDisabled: {
        backgroundColor: '#6C757D',
        shadowOpacity: 0.1,
    },
    submitButtonText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: 'white',
    },
    // Multi-date styles
    addDateButton: {
        backgroundColor: '#3674B5',
        borderRadius: 8,
        paddingVertical: 12,
        paddingHorizontal: 16,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginTop: 12,
        gap: 8,
    },
    addDateButtonText: {
        color: 'white',
        fontSize: 14,
        fontWeight: '600',
    },
    selectedDatesContainer: {
        marginTop: 16,
    },
    selectedDatesList: {
        flexDirection: 'row',
        gap: 8,
        paddingHorizontal: 16,
    },
    selectedDateChip: {
        backgroundColor: '#E3F2FD',
        borderRadius: 20,
        paddingVertical: 8,
        paddingHorizontal: 12,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        borderWidth: 1,
        borderColor: '#3674B5',
    },
    selectedDateText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#3674B5',
    },
    removeDateButton: {
        padding: 2,
    },
    dateTabsContainer: {
        marginVertical: 16,
    },
    dateTabsList: {
        flexDirection: 'row',
        gap: 12,
        paddingHorizontal: 16,
    },
    dateTab: {
        backgroundColor: '#F8F9FA',
        borderRadius: 12,
        paddingVertical: 12,
        paddingHorizontal: 16,
        borderWidth: 1,
        borderColor: '#E9ECEF',
        minWidth: 100,
        alignItems: 'center',
    },
    dateTabActive: {
        backgroundColor: '#3674B5',
        borderColor: '#3674B5',
    },
    dateTabText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#2C3E50',
    },
    dateTabTextActive: {
        color: 'white',
    },
    dateTabSlotCount: {
        fontSize: 10,
        color: '#6C757D',
        marginTop: 2,
    },
    dateTabSlotCountActive: {
        color: 'rgba(255, 255, 255, 0.8)',
    },
    currentDateSlotsContainer: {
        marginTop: 16,
    },
    currentDateTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: '#2C3E50',
        marginBottom: 12,
    },
    emptyDatesState: {
        alignItems: 'center',
        paddingVertical: 40,
        paddingHorizontal: 20,
    },
    emptyDatesText: {
        fontSize: 18,
        fontWeight: '600',
        color: '#ADB5BD',
        marginTop: 16,
        textAlign: 'center',
    },
    emptyDatesSubtext: {
        fontSize: 14,
        color: '#6C757D',
        marginTop: 8,
        textAlign: 'center',
        lineHeight: 20,
    },
});

export default DockDetail;