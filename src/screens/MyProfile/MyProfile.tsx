import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    SafeAreaView,
    ScrollView,
    Image,
    Alert,
} from 'react-native';
import { Paths } from '@/navigation/paths';
import { RootScreenProps } from '@/navigation/types';
import { logoutUser } from '@/config/authService';
import Icon from 'react-native-vector-icons/Ionicons';
import { auth } from '@/config/firebase';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { rm } from '@/utils/scaling';

type Props = RootScreenProps<Paths.MyProfile>;

const MyProfile: React.FC<Props> = ({ navigation }) => {
    const [userRole, setUserRole] = useState<string>('customer');
    const [userName, setUserName] = useState<string>('');
    const [userEmail, setUserEmail] = useState<string>('');

    useEffect(() => {
        loadUserData();
    }, []);

    const loadUserData = async () => {
        try {
            const role = await AsyncStorage.getItem('userRole');
            setUserRole(role || 'customer');

            const user = auth.currentUser;
            if (user) {
                setUserEmail(user.email || '');
                setUserName(user.displayName || user.email?.split('@')[0] || 'User');
            }
        } catch (error) {
            console.error('Error loading user data:', String(error));
        }
    };

    const ProfileOption = ({
        iconName,
        label,
        isDestructive = false,
        onPress,
        showBadge = false,
        badgeText = '',
    }: {
        iconName: string;
        label: string;
        isDestructive?: boolean;
        onPress: () => void;
        showBadge?: boolean;
        badgeText?: string;
    }) => (
        <TouchableOpacity style={styles.optionRow} onPress={onPress} activeOpacity={0.7}>
            <View style={styles.optionContent}>
                <View style={[styles.iconContainer, isDestructive && styles.destructiveIconContainer]}>
                    <Icon
                        name={iconName}
                        size={22}
                        color={isDestructive ? '#FF4444' : '#3674B5'}
                    />
                </View>
                <Text style={[styles.optionText, isDestructive && styles.destructiveText]}>
                    {label}
                </Text>
                {showBadge && (
                    <View style={styles.badge}>
                        <Text style={styles.badgeText}>{badgeText}</Text>
                    </View>
                )}
            </View>
            <Icon name="chevron-forward" size={20} color="#C7C7CC" />
        </TouchableOpacity>
    );


    const logout = () => {
        Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
                {
                    text: 'Cancel',
                    onPress: () => console.log('Cancel Pressed'),
                    style: 'cancel',
                },
                {
                    text: 'OK',
                    onPress: async () => {
                        try {
                            await logoutUser();
                            console.log("Successfully logged out");
                        } catch (error) {
                            console.error('Logout error:', String(error));
                            Alert.alert(
                                'Logout Error',
                                'Failed to logout. Please try again.',
                                [{ text: 'OK' }]
                            );
                        }
                    }
                },
            ],
        )
    };

    return (
        <SafeAreaView style={styles.safeArea}>
            <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
                {/* Profile Header */}
                {/* <View style={styles.profileHeader}>
                    <View style={styles.avatarContainer}>
                        <View style={styles.avatar}>
                            <Icon name="person" size={40} color="#3674B5" />
                        </View>
                        <View style={styles.roleIndicator}>
                            <Icon
                                name={userRole === 'owner' ? 'business' : 'person'}
                                size={16}
                                color="white"
                            />
                        </View>
                    </View>
                    <View style={styles.userInfo}>
                        <Text style={styles.userName}>{userName}</Text>
                        <Text style={styles.userEmail}>{userEmail}</Text>
                        <View style={styles.roleChip}>
                            <Text style={styles.roleText}>
                                {userRole === 'owner' ? 'Dock Owner' : 'Customer'}
                            </Text>
                        </View>
                    </View>
                </View> */}

                {/* Profile Options */}
                <View style={styles.optionsContainer}>
                    <View style={styles.optionsSection}>
                        <Text style={styles.sectionTitle}>Account</Text>
                        <View style={styles.optionsGroup}>
                            <ProfileOption
                                iconName="person-circle-outline"
                                label="Account Details"
                                onPress={() => navigation.navigate(Paths.AccountDetail)}
                            />
                            <ProfileOption
                                iconName="swap-horizontal-outline"
                                label="Switch Profile"
                                onPress={() => navigation.navigate(Paths.SwitchProfile)}
                                showBadge={true}
                                badgeText={userRole === 'owner' ? 'Customer' : 'Owner'}
                            />
                        </View>
                    </View>

                    <View style={styles.optionsSection}>
                        <Text style={styles.sectionTitle}>Security</Text>
                        <View style={styles.optionsGroup}>
                            <ProfileOption
                                iconName="lock-closed-outline"
                                label="Change Password"
                                onPress={() => navigation.navigate(Paths.ChangePassword)}
                            />
                        </View>
                    </View>

                    <View style={styles.optionsSection}>
                        <Text style={styles.sectionTitle}>Danger Zone</Text>
                        <View style={styles.optionsGroup}>
                            <ProfileOption
                                iconName="trash-outline"
                                label="Delete Account"
                                onPress={() => navigation.navigate(Paths.DeleteAccount)}
                                isDestructive={true}
                            />
                            <ProfileOption
                                iconName="log-out-outline"
                                label="Logout"
                                onPress={() => logout()}
                                isDestructive={true}
                            />
                        </View>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};


const styles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: '#F8F9FA',
    },
    container: {
        flex: 1,
        paddingTop: rm(20)
    },
    profileHeader: {
        backgroundColor: 'white',
        paddingHorizontal: rm(24),
        paddingVertical: rm(32),
        alignItems: 'center',
        borderBottomWidth: 1,
        borderBottomColor: '#E9ECEF',
        marginBottom: rm(24),
    },
    avatarContainer: {
        position: 'relative',
        marginBottom: rm(16),
    },
    avatar: {
        width: rm(80),
        height: rm(80),
        borderRadius: rm(40),
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: rm(3),
        borderColor: '#3674B5',
    },
    roleIndicator: {
        position: 'absolute',
        bottom: rm(-2),
        right: rm(-2),
        width: rm(28),
        height: rm(28),
        borderRadius: rm(14),
        backgroundColor: '#3674B5',
        justifyContent: 'center',
        alignItems: 'center',
        borderWidth: rm(3),
        borderColor: 'white',
    },
    userInfo: {
        alignItems: 'center',
    },
    userName: {
        fontSize: rm(24),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(4),
        textTransform: 'capitalize',
    },
    userEmail: {
        fontSize: rm(16),
        color: '#6C757D',
        marginBottom: rm(12),
    },
    roleChip: {
        backgroundColor: '#3674B5',
        paddingHorizontal: rm(16),
        paddingVertical: rm(6),
        borderRadius: rm(20),
    },
    roleText: {
        color: 'white',
        fontSize: rm(14),
        fontWeight: '600',
    },
    optionsContainer: {
        paddingHorizontal: rm(20),
        paddingBottom: rm(32),
    },
    optionsSection: {
        marginBottom: rm(32),
    },
    sectionTitle: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: '#2C3E50',
        marginBottom: rm(16),
        paddingHorizontal: rm(4),
    },
    optionsGroup: {
        backgroundColor: 'white',
        borderRadius: 16,
        overflow: 'hidden',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 4,
    },
    optionRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 20,
        paddingVertical: 16,
        borderBottomWidth: 1,
        borderBottomColor: '#F8F9FA',
    },
    optionContent: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    iconContainer: {
        width: 40,
        height: 40,
        borderRadius: 20,
        backgroundColor: '#E3F2FD',
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 16,
    },
    destructiveIconContainer: {
        backgroundColor: '#FFEBEE',
    },
    optionText: {
        fontSize: 16,
        fontWeight: '500',
        color: '#2C3E50',
        flex: 1,
    },
    destructiveText: {
        color: '#FF4444',
    },
    badge: {
        backgroundColor: '#28A745',
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
        marginLeft: 8,
    },
    badgeText: {
        color: 'white',
        fontSize: 12,
        fontWeight: '600',
    },
});


export default MyProfile;

