import React, { useState, useEffect, useContext } from 'react';
import { View, Text, ActivityIndicator, Alert, Dimensions } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { AuthContext } from '@/config/AuthProvider';
import { db } from '@/config/firebase';
import { useTheme } from '@/theme';
import { SafeScreen } from '@/components/templates';
import { useFocusEffect } from '@react-navigation/native';

// Import the existing dashboard components
import OwnerDashboard from '@/screens/OwnerDashboard/OwnerDashboard';
import CustomerDashboard from '@/screens/CustomerDashboard/CustomerDashboard';
import RoleSelector from '@/components/RoleSelector/RoleSelector';

type UserRole = 'owner' | 'customer' | null;

function DashboardTab() {
    const { user } = useContext(AuthContext);
    const { colors, layout } = useTheme();
    const [userRole, setUserRole] = useState<UserRole>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        if (user) {
            fetchUserRole();
        }
    }, [user]);

    // Listen for role changes when the tab comes into focus
    useFocusEffect(
        React.useCallback(() => {
            if (user) {
                console.log('Dashboard tab focused, re-checking user role...');
                fetchUserRole();
            }
        }, [user])
    );

    const fetchUserRole = async () => {
        if (!user) {
            console.log('No user found in fetchUserRole');
            return;
        }

        try {
            setLoading(true);
            console.log('Fetching user role for:', user.uid);

            // First, try to get role from AsyncStorage (consistent with profile switching)
            const storedRole = await AsyncStorage.getItem('userRole');
            console.log('Stored role from AsyncStorage:', storedRole);
            if (storedRole) {
                console.log('Using stored role:', storedRole);
                setUserRole(storedRole as UserRole);
                setLoading(false);
                return;
            }

            // If not in AsyncStorage, check legacy storage
            const storedUserData = await AsyncStorage.getItem('myUser');
            if (storedUserData) {
                const userData = JSON.parse(storedUserData);
                if (userData.role) {
                    // Migrate to new storage format
                    await AsyncStorage.setItem('userRole', userData.role);
                    setUserRole(userData.role);
                    setLoading(false);
                    return;
                }
            }

            // If not in AsyncStorage, fetch from Firestore
            const userDocRef = doc(db, 'users', user.uid);
            const userDocSnap = await getDoc(userDocRef);

            if (userDocSnap.exists()) {
                const userData = userDocSnap.data();
                const role = userData.role as UserRole;

                if (role) {
                    setUserRole(role);
                    // Store in new format
                    await AsyncStorage.setItem('userRole', role);
                } else {
                    // User exists but no role set
                    setUserRole(null);
                }
            } else {
                // User document doesn't exist, create it
                await setDoc(doc(db, 'users', user.uid), {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName,
                    createdAt: new Date(),
                });
                setUserRole(null);
            }
        } catch (error) {
            console.error('Error fetching user role:', error);
            Alert.alert('Error', 'Failed to load user data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleRoleSelection = async (role: 'owner' | 'customer') => {
        if (!user) return;

        try {
            setLoading(true);

            // Update Firestore
            const userDocRef = doc(db, 'users', user.uid);
            await setDoc(userDocRef, { role }, { merge: true });

            // Update AsyncStorage with new format
            await AsyncStorage.setItem('userRole', role);

            // Update legacy storage for backward compatibility
            const storedUserData = await AsyncStorage.getItem('myUser');
            if (storedUserData) {
                const userData = JSON.parse(storedUserData);
                const updatedUserData = { ...userData, role };
                await AsyncStorage.setItem('myUser', JSON.stringify(updatedUserData));
            }

            setUserRole(role);
        } catch (error) {
            console.error('Error saving user role:', error);
            Alert.alert('Error', 'Failed to save role selection. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return (
            // <SafeScreen>
            <View style={[
                layout.flex_1,
                layout.justifyCenter,
                layout.itemsCenter,
                { paddingHorizontal: 20 }
            ]}>
                <ActivityIndicator size="large" color="#3674B5" />
                <Text style={{
                    marginTop: 16,
                    color: colors.gray800,
                    fontSize: 16,
                    textAlign: 'center'
                }}>
                    Loading your dashboard...
                </Text>
            </View>
            // </SafeScreen>
        );
    }

    // If no role is selected, show role selector
    if (!userRole) {
        console.log('No user role found, showing role selector');
        return <RoleSelector onRoleSelect={handleRoleSelection} />;
    }

    // Render appropriate dashboard based on role
    console.log('Rendering dashboard for role:', userRole);
    if (userRole === 'owner') {
        console.log('Rendering OwnerDashboard');
        return <OwnerDashboard key={`dashboard-${userRole}-${user?.uid}`} navigation={undefined as any} route={undefined as any} />;
    } else {
        console.log('Rendering CustomerDashboard');
        return <CustomerDashboard key={`dashboard-${userRole}-${user?.uid}`} navigation={undefined as any} route={undefined as any} />;
    }
}

export default DashboardTab;
