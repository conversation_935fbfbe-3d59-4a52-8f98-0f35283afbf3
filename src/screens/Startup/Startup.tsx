import type { RootScreenProps } from '@/navigation/types';
import { useTranslation } from 'react-i18next';
import { Text, TouchableOpacity, View } from 'react-native';
import { Paths } from '@/navigation/paths';
import { useTheme } from '@/theme';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';

function Startup({ navigation }: RootScreenProps<Paths.Startup>) {
  const { layout } = useTheme();
  const { t } = useTranslation();

  // const { isError, isFetching, isSuccess } = useQuery({
  //   queryFn: () => {
  //     return Promise.resolve(true);
  //   },
  //   queryKey: ['startup'],
  // });

  // useEffect(() => {
  //   if (isSuccess) {
  //     navigation.reset({
  //       index: 0,
  //       routes: [{ name: Paths.Example }],
  //     });
  //   }
  // }, [isSuccess, navigation]);
  return (
    <SafeScreen>
      <View
        style={[
          layout.flex_1,
          layout.col,
          layout.itemsCenter,
          layout.justifyCenter,
        ]}
      >
        <AssetByVariant path="Logo" resizeMode="contain" />
        <Text style={{ fontSize: 18, fontWeight: 'bold', color: "#3674B5", padding: 10 }}>
          {t('common.title')}
        </Text>
        <Text style={{ fontSize: 14, color: "#717171", marginHorizontal: 46, textAlign: 'center' }}>
          {t('common.tagLine')}
        </Text>
      </View>
      <TouchableOpacity style={{ marginBottom: 20, justifyContent: 'center', alignItems: 'center', height: 50, marginHorizontal: 20, borderRadius: 8, backgroundColor: "#3674B5" }} onPress={() => navigation.navigate(Paths.Login)}>
        <Text style={{ fontSize: 14, color: "white", fontWeight: '600' }}>
          {t('common.getStarted')}
        </Text>
      </TouchableOpacity>
    </SafeScreen>

  );
}

export default Startup;
