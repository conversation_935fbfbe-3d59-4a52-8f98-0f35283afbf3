import type { Paths } from '@/navigation/paths';
import type { StackScreenProps } from '@react-navigation/stack';
import type { BottomTabScreenProps } from '@react-navigation/bottom-tabs';

export type RootScreenProps<
  S extends keyof RootStackParamList = keyof RootStackParamList,
> = StackScreenProps<RootStackParamList, S>;

export type TabScreenProps<S extends keyof TabParamList = keyof TabParamList> =
  BottomTabScreenProps<TabParamList, S>;

export type RootStackParamList = {
  [Paths.Example]: undefined;
  [Paths.Startup]: undefined;
  [Paths.Login]: undefined;
  [Paths.Register]: undefined;
  [Paths.ForgotPassword]: undefined;
  [Paths.Role]: undefined;
  [Paths.OwnerDashboard]: undefined;
  [Paths.DockDetail]: undefined;
  [Paths.MyProfile]: undefined;
  [Paths.AccountDetail]: undefined;
  [Paths.SwitchProfile]: undefined;
  [Paths.ChangePassword]: undefined;
  [Paths.DeleteAccount]: undefined;
  [Paths.CustomerDashboard]: undefined;
  [Paths.ViewDock]: undefined;
  [Paths.DockBooking]: undefined;
  [Paths.ReviewBooking]: undefined;
  [Paths.AddReview]: {
    dockId: string;
    dockName: string;
    dockImage?: string;
  };
  // Tab Navigation
  [Paths.MainTabs]: undefined;
};

export type TabParamList = {
  [Paths.Dashboard]: undefined;
  [Paths.Booking]: undefined;
  [Paths.Profile]: undefined;
};
