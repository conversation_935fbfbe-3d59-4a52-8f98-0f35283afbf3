import type { RootStackParamList } from '@/navigation/types';

import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { Paths } from '@/navigation/paths';
import { useTheme } from '@/theme';

import { Example, Login, Startup, Register, ForgotPassword, Role, OwnerDashboard, DockDetail, DeleteAccount, CustomerDashboard, ViewDock, DockBooking, MapDashboard } from '@/screens';
import MyProfile from '@/screens/MyProfile/MyProfile';
import AccountDetail from '@/screens/AccountDetail/AccountDetail';
import SwitchProfile from '@/screens/SwitchProfile/SwitchProfile';
import ChangePassword from '@/screens/ChangePassword/ChangePassword';
import { AuthContext } from '@/config/AuthProvider';
import { useContext } from 'react';
import { ActivityIndicator, StyleSheet, View, SafeAreaView } from 'react-native';
import { navigationRef } from '@/config/navigationRef';
import ReviewBooking from '@/screens/ReviewBooking/ReviewBooking';
import AddReview from '@/screens/AddReview/AddReview';
import TabNavigator from '@/navigation/TabNavigator';

const Stack = createStackNavigator<RootStackParamList>();

function ApplicationNavigator() {
  const { navigationTheme, variant } = useTheme();
  const { user, loading } = useContext(AuthContext);
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={navigationTheme.colors.primary} />
      </View>
    );
  }
  return (
    <SafeAreaView style={styles.safeArea}>
      <NavigationContainer ref={navigationRef}>
        <Stack.Navigator
          key={variant}
          screenOptions={{
            gestureDirection: "vertical",
            headerShown: true,
            gestureEnabled: false,
            animation: "fade_from_bottom",
            headerStyle: {
              backgroundColor: 'white',
            },
            headerTintColor: 'black',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 18,
              color: 'black',
            },
            headerBackTitle: '',

          }}
        >
          {user ? (
            <>
              {/* Main Tab Navigation - Primary authenticated screen */}
              <Stack.Screen
                component={TabNavigator}
                name={Paths.MainTabs}
                options={{ headerShown: false, }}
              />

              {/* Modal/Detail screens that should be presented over tabs */}
              <Stack.Screen
                component={DockDetail}
                name={Paths.DockDetail}
                options={{ headerTitle: 'Dock Details' }}
              />
              <Stack.Screen
                component={DockBooking}
                name={Paths.DockBooking}
                options={{ headerTitle: 'Book Dock' }}
              />
              <Stack.Screen
                component={ViewDock}
                name={Paths.ViewDock}
                options={{ headerTitle: 'View Dock' }}
              />
              <Stack.Screen
                component={AccountDetail}
                name={Paths.AccountDetail}
                options={{ headerTitle: 'Account Details' }}
              />
              <Stack.Screen
                component={SwitchProfile}
                name={Paths.SwitchProfile}
                options={{ headerTitle: 'Switch Profile' }}
              />
              <Stack.Screen
                component={ChangePassword}
                name={Paths.ChangePassword}
                options={{ headerTitle: 'Change Password' }}
              />
              <Stack.Screen
                component={DeleteAccount}
                name={Paths.DeleteAccount}
                options={{ headerTitle: 'Delete Account' }}
              />
              <Stack.Screen
                component={ReviewBooking}
                name={Paths.ReviewBooking}
                options={{ headerTitle: 'Review Booking' }}
              />
              <Stack.Screen
                component={AddReview}
                name={Paths.AddReview}
                options={{ headerTitle: 'Add Review' }}
              />
              <Stack.Screen
                component={Example}
                name={Paths.Example}
                options={{ headerTitle: 'Example' }}
              />

              {/* Keep individual dashboard screens for backward compatibility */}
              <Stack.Screen
                component={OwnerDashboard}
                name={Paths.OwnerDashboard}
                options={{ headerTitle: 'Owner Dashboard' }}
              />
              <Stack.Screen
                component={CustomerDashboard}
                name={Paths.CustomerDashboard}
                options={{ headerTitle: 'Customer Dashboard' }}
              />
              <Stack.Screen
                component={MyProfile}
                name={Paths.MyProfile}
                options={{ headerTitle: 'My Profile' }}
              />
              <Stack.Screen
                component={Role}
                name={Paths.Role}
                options={{ headerTitle: 'Select Role' }}
              />
            </>
          ) : (
            <>
              {/* <Stack.Screen
                component={MapDashboard}
                name={Paths.MapDashboard}
                options={{ headerShown: false }}
              /> */}
              <Stack.Screen
                component={Startup}
                name={Paths.Startup}
                options={{ headerShown: false }}
              />
              <Stack.Screen
                component={Login}
                name={Paths.Login}
                options={{ headerTitle: 'Login' }}
              />
              <Stack.Screen
                component={Register}
                name={Paths.Register}
                options={{ headerTitle: 'Register' }}
              />
              <Stack.Screen
                component={ForgotPassword}
                name={Paths.ForgotPassword}
                options={{ headerShown: false }}
              />
            </>)}
        </Stack.Navigator>
      </NavigationContainer>
    </SafeAreaView >
  );
}

export default ApplicationNavigator;

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  safeArea: {
    flex: 1,
    backgroundColor: "white"
  }
});
