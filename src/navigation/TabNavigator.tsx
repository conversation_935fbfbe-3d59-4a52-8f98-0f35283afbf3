import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Paths } from '@/navigation/paths';
import type { TabParamList } from '@/navigation/types';
import Icon from 'react-native-vector-icons/Ionicons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Import screens
import Booking from '@/screens/Booking/Booking';
import MyProfile from '@/screens/MyProfile/MyProfile';
import DashboardTab from '@/screens/DashboardTab/DashboardTab';

const Tab = createBottomTabNavigator<TabParamList>();

function TabNavigator() {
  const insets = useSafeAreaInsets();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          if (route.name === Paths.Dashboard) {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === Paths.Booking) {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === Paths.Profile) {
            iconName = focused ? 'person' : 'person-outline';
          } else {
            iconName = 'help-outline';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: 'black',
        tabBarInactiveTintColor: 'gray',
        tabBarStyle: {
          backgroundColor: 'white',
          borderTopWidth: 1,
          borderTopColor: '#f0f0f0',
          paddingBottom: Math.max(insets.bottom, 5), // Use safe area bottom or minimum 5
          paddingTop: 5,
          height: 70 + Math.max(insets.bottom - 5, 0), // Adjust height for safe area
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: true,
        headerStyle: {
          backgroundColor: 'white',
          // Let the navigation handle safe area for headers
        },
        headerTintColor: 'black',
        headerTitleStyle: {
          fontWeight: 'bold',
          fontSize: 18,
          color: 'black',
        },
      })}
    >
      <Tab.Screen
        name={Paths.Dashboard}
        component={DashboardTab}
        options={{
          tabBarLabel: 'Dashboard',
          headerTitle: 'Dashboard',
        }}
      />
      <Tab.Screen
        name={Paths.Booking}
        component={Booking}
        options={{
          tabBarLabel: 'Booking',
          headerTitle: 'Booking',
        }}
      />
      <Tab.Screen
        name={Paths.Profile}
        component={MyProfile as any}
        options={{
          tabBarLabel: 'Profile',
          headerTitle: 'Profile',
        }}
      />
    </Tab.Navigator>
  );
}

export default TabNavigator;
