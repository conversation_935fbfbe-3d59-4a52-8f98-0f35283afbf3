// src/config/firebase.js (or .ts)
import { initializeApp } from 'firebase/app';
import { getFirestore } from 'firebase/firestore';
import {
  initializeAuth,
  getReactNativePersistence,
  getAuth,
} from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';

const firebaseConfig = {
  apiKey: 'AIzaSyBKdoBMtG6u_yQv5z6aUrJTI4DNNo4RZNY',
  //   authDomain: 'YOUR_AUTH_DOMAIN',
  projectId: 'dawson-lone-launcher',
  storageBucket: 'dawson-lone-launcher.firebasestorage.app',
  messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
  appId: '1:360866724295:ios:0376676fc87a97ebd541e2',
};

// Initialize Firebase app ONLY ONCE
const app = initializeApp(firebaseConfig);

// Get Auth and Firestore instances
// const auth = getAuth(app);

const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage),
});

const db = getFirestore(app);

// Export them for use in other components
export { auth, db, app };
