import { signOut, reauthenticateWithCredential, EmailAuthProvider, deleteUser, sendPasswordResetEmail } from 'firebase/auth';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { auth } from '@/config/firebase';

export async function logoutUser() {
  try {
    // Clear all user-related data from AsyncStorage
    await AsyncStorage.multiRemove([
      'currentUser',
      'myUser',
      'userRole'
    ]);

    // Sign out from Firebase - this will trigger the AuthProvider's onAuthStateChanged
    // which will automatically handle navigation to the login screen
    await signOut(auth);

    console.log('User logged out and storage cleared.');
  } catch (error) {
    console.error('Logout failed:', error);
    throw error; // Re-throw so the UI can handle the error
  }
}

export async function deleteAccount(email: string, password: string) {
  try {
    const user = auth.currentUser;

    if (!user) throw new Error("No user is currently logged in");

    // Reauthenticate user (required for sensitive operations)
    const credential = EmailAuthProvider.credential(email, password);
    await reauthenticateWithCredential(user, credential);

    // Delete user account
    await deleteUser(user);

    console.log("User account deleted successfully");

    // You may want to clear any local storage or navigate to login screen here
  } catch (error) {
    console.error("Failed to delete account:", error);
    // Handle error (e.g., ask user to re-login, show error message)
  }
}

export async function sendPasswordReset(email: string) {
  try {
    await sendPasswordResetEmail(auth, email);
    console.log('Password reset email sent successfully');
    return { success: true };
  } catch (error: any) {
    console.error('Failed to send password reset email:', String(error));

    let errorMessage = 'Failed to send reset email. Please try again.';
    if (error.code) {
      switch (error.code) {
        case 'auth/user-not-found':
          errorMessage = 'No account found with this email address.';
          break;
        case 'auth/invalid-email':
          errorMessage = 'Invalid email address.';
          break;
        case 'auth/too-many-requests':
          errorMessage = 'Too many requests. Please try again later.';
          break;
        default:
          errorMessage = error.message || errorMessage;
      }
    }

    return { success: false, error: errorMessage };
  }
}