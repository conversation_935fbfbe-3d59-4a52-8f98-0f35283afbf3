// navigationRef.ts
import { Paths } from '@/navigation/paths';
import {
  createNavigationContainerRef,
  CommonActions,
} from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

export function resetToLogin() {
  if (navigationRef.isReady()) {
    try {
      navigationRef.dispatch(
        CommonActions.reset({
          index: 0,
          routes: [{ name: Paths.Startup }], // Reset to startup which will handle auth flow
        }),
      );
    } catch (error) {
      console.error('Navigation reset failed:', error);
      // Fallback: try to navigate to startup
      try {
        navigationRef.navigate(Paths.Startup as never);
      } catch (fallbackError) {
        console.error('Fallback navigation failed:', fallbackError);
      }
    }
  }
}

export function navigateToLogin() {
  if (navigationRef.isReady()) {
    try {
      navigationRef.navigate(Paths.Login as never);
    } catch (error) {
      console.error('Navigate to login failed:', error);
    }
  }
}
