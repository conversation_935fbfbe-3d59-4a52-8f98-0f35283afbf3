// Add the interface import if it's in a separate file (recommended)

export function formatFirestoreTimestamps(
    date?: any,         // Use the defined interface
    startTime?: any,    // Use the defined interface
    endTime?: any       // Use the defined interface
): string { // <--- Recommend changing 'any' to 'string' as it always returns a string

    const convert = (ts?: any) => { // Use interface here too
        if (!ts || typeof ts.seconds !== 'number' || typeof ts.nanoseconds !== 'number') {
            return null; // Return null if invalid, consistent with convertFirestoreTimestampToDate
        }
        return new Date(ts.seconds * 1000 + ts.nanoseconds / 1e6);
    };

    const formatDate = (d: Date | null) =>
        d ? d.toLocaleDateString() : 'Invalid date';

    const formatTime = (d: Date | null) =>
        d ? d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '--:--';

    const dateObj = convert(date);
    const startObj = convert(startTime);
    const endObj = convert(endTime);

    // This will produce a string like "6/15/2025 09:00 AM 05:00 PM" (depending on locale and time values)
    return `${formatDate(dateObj)} ${formatTime(startObj)} ${formatTime(endObj)}`;
}
export const convertTime = (ts?: any) => { // Use interface here too
    if (!ts || typeof ts.seconds !== 'number' || typeof ts.nanoseconds !== 'number') {
        return null; // Return null if invalid, consistent with convertFirestoreTimestampToDate
    }
    return new Date(ts.seconds * 1000 + ts.nanoseconds / 1e6).toDateString();
};