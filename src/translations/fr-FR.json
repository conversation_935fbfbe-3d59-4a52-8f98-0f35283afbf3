{"myapp": {"common_appName": {"full": "MyApp React Native", "initials": "RNB"}, "common_error": "<PERSON><PERSON><PERSON> chose s'est mal passé.", "screen_example": {"hello_user": "<PERSON>ut, je m'appelle {{name}}", "title": "Bienvenue sur Le $t(common_appName.full)", "description": "Vous voulez découvrir des fonctionnalités ? Cliquez sur l'un des trois boutons en bas de l'écran. Le premier vous permet d'appeler une API REST. Le deuxième de changer la couleur du thème. Et le troisième de changer la langue"}, "error_boundary": {"title": "Oups ! Quelque chose s'est mal passé.", "description": "Nous sommes désolés pour le désagrément. Veuillez réessayer plus tard.", "cta": "Recharger l'écran"}, "common": {"title": "<PERSON><PERSON> <PERSON>", "tagLine": "Gestion intelligente des quais pour propriétaires et marins.", "getStarted": "Commencer"}}}