import {
  collection,
  query,
  where,
  getDocs,
  orderBy,
  Timestamp,
} from 'firebase/firestore';
import { db } from '@/config/firebase';

export interface BookingStatus {
  isBooked: boolean;
  bookedSlots: string[];
  availableSlots: string[];
  totalSlots: number;
  remainingSlots: number;
}

// Complete booking data structure
export interface BookingData {
  id: string;
  bookingUserId: string;
  name: string; // Dock name
  // Multi-date support
  selectedDates?: string[]; // Array of selected date strings
  selectedDateSlots?: { [date: string]: string[] }; // Object mapping dates to slot arrays
  numberOfDates?: number;
  // Legacy support
  availableDate: any; // Firestore Timestamp
  selectedSlots: string[]; // Array of slot IDs
  selectedSlotDetails: any[]; // Full slot details
  pricePerSlot: number;
  totalPrice: number;
  numberOfSlots: number;
  dockId: string;
  dockImageUrl: string;
  status: 'Pending' | 'Approved' | 'Cancelled' | 'Completed';
  createdAt: Timestamp;
  customerDetails: {
    fullName: string;
    contact: string;
    email: string;
    boatName: string;
    boatType: string;
    boatLength: string;
    boatWidth: string;
    registrationNumber: string;
    message: string;
  };
}

// Simplified booking for display
export interface BookingDisplay {
  id: string;
  dockName: string;
  dockImage: string;
  bookingDate: Date;
  dateDisplay?: string; // Formatted date display for multi-date bookings
  timeSlots: string[];
  totalPrice: number;
  status: string;
  customerName: string;
  customerContact: string;
  numberOfSlots: number;
  numberOfDates?: number; // Number of dates in multi-date booking
  dockId: string;
}

export const checkDockBookingStatus = async (
  dockId: string,
  timeSlots: any[],
): Promise<BookingStatus> => {
  try {
    const bookingRef = collection(db, 'booking');
    const q = query(bookingRef, where('dockId', '==', dockId));
    const querySnapshot = await getDocs(q);

    const bookedSlots: string[] = [];

    // Collect all booked slot IDs for this dock
    querySnapshot.forEach((doc) => {
      const bookingData = doc.data();
      if (
        bookingData.selectedSlots &&
        Array.isArray(bookingData.selectedSlots)
      ) {
        bookedSlots.push(...bookingData.selectedSlots);
      }
    });

    // Remove duplicates
    const uniqueBookedSlots = [...new Set(bookedSlots)];

    // Get all slot IDs from timeSlots
    const allSlotIds = timeSlots.map((slot) => slot.id);

    // Find available slots
    const availableSlots = allSlotIds.filter(
      (slotId) => !uniqueBookedSlots.includes(slotId),
    );

    const totalSlots = allSlotIds.length;
    const remainingSlots = availableSlots.length;
    const isBooked = remainingSlots === 0; // Fully booked if no slots remaining

    return {
      isBooked,
      bookedSlots: uniqueBookedSlots,
      availableSlots,
      totalSlots,
      remainingSlots,
    };
  } catch (error) {
    console.error('Error checking booking status:', error);
    // Return default status on error
    return {
      isBooked: false,
      bookedSlots: [],
      availableSlots: timeSlots.map((slot) => slot.id),
      totalSlots: timeSlots.length,
      remainingSlots: timeSlots.length,
    };
  }
};

export const isSlotBooked = (
  slotId: string,
  bookedSlots: string[],
): boolean => {
  return bookedSlots.includes(slotId);
};

// Get all bookings for a customer
export const getCustomerBookings = async (
  userId: string,
): Promise<BookingDisplay[]> => {
  try {
    const bookingRef = collection(db, 'booking');

    // First try with ordering (requires composite index)
    let bookingQuery = query(
      bookingRef,
      where('bookingUserId', '==', userId),
      orderBy('createdAt', 'desc'),
    );

    let querySnapshot;
    try {
      querySnapshot = await getDocs(bookingQuery);
    } catch (indexError) {
      console.warn(
        'Composite index not available for customer bookings, falling back to simple query:',
        indexError,
      );
      // Fallback to simple query without ordering
      try {
        bookingQuery = query(bookingRef, where('bookingUserId', '==', userId));
        querySnapshot = await getDocs(bookingQuery);
      } catch (fallbackError) {
        console.error(
          'Customer bookings fallback query also failed:',
          fallbackError,
        );
        throw fallbackError; // Re-throw since we can't continue without data
      }
    }

    const bookings: BookingDisplay[] = [];

    querySnapshot.forEach((doc) => {
      const data = doc.data() as BookingData;

      // Format time slots for display
      const timeSlots =
        data.selectedSlotDetails?.map((slot: any) => {
          const startTime = slot.startTime?.toDate?.() || new Date();
          const endTime = slot.endTime?.toDate?.() || new Date();
          return `${startTime.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })} - ${endTime.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
          })}`;
        }) || [];

      // Handle multi-date bookings
      let bookingDate = data.availableDate?.toDate?.() || new Date();
      let dateDisplay = bookingDate.toLocaleDateString();

      if (data.selectedDates && data.selectedDates.length > 0) {
        // Multi-date booking
        const sortedDates = data.selectedDates.sort();
        bookingDate = new Date(sortedDates[0]); // Use first date as primary

        if (sortedDates.length === 1) {
          dateDisplay = new Date(sortedDates[0]).toLocaleDateString();
        } else {
          const firstDate = new Date(sortedDates[0]).toLocaleDateString(
            'en-US',
            { month: 'short', day: '2-digit' },
          );
          const lastDate = new Date(
            sortedDates[sortedDates.length - 1],
          ).toLocaleDateString('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
          });
          dateDisplay = `${firstDate} - ${lastDate} (${sortedDates.length} dates)`;
        }
      }

      bookings.push({
        id: doc.id,
        dockName: data.name,
        dockImage: data.dockImageUrl,
        bookingDate: bookingDate,
        dateDisplay: dateDisplay, // Add formatted date display
        timeSlots,
        totalPrice: data.totalPrice,
        status: data.status,
        customerName: data.customerDetails.fullName,
        customerContact: data.customerDetails.contact,
        numberOfSlots: data.numberOfSlots,
        numberOfDates: data.numberOfDates || 1,
        dockId: data.dockId,
      });
    });

    // Sort manually by creation date (newest first) in case we used the fallback query
    bookings.sort((a, b) => {
      return b.bookingDate.getTime() - a.bookingDate.getTime();
    });

    return bookings;
  } catch (error) {
    console.error('Error fetching customer bookings:', error);
    return [];
  }
};

// Get all bookings for docks owned by a specific user (owner view)
export const getOwnerDockBookings = async (
  ownerId: string,
): Promise<BookingDisplay[]> => {
  try {
    console.log('Fetching owner dock bookings for user:', ownerId);

    // First, get all docks owned by this user
    const docksRef = collection(db, 'docks');
    const docksQuery = query(docksRef, where('userId', '==', ownerId));
    const docksSnapshot = await getDocs(docksQuery);

    if (docksSnapshot.empty) {
      return [];
    }

    // Get all dock IDs owned by this user
    const ownedDockIds: string[] = [];
    docksSnapshot.forEach((doc) => {
      ownedDockIds.push(doc.id);
    });

    // Get all bookings for these docks
    const bookingRef = collection(db, 'booking');
    const allBookings: BookingDisplay[] = [];

    // Firebase doesn't support 'in' queries with more than 10 items, so we batch them
    const batchSize = 10;
    for (let i = 0; i < ownedDockIds.length; i += batchSize) {
      const batch = ownedDockIds.slice(i, i + batchSize);

      let bookingQuery = query(
        bookingRef,
        where('dockId', 'in', batch),
        orderBy('createdAt', 'desc'),
      );

      let querySnapshot;
      try {
        querySnapshot = await getDocs(bookingQuery);
      } catch (indexError) {
        console.warn(
          'Composite index not available for owner bookings, falling back to simple query:',
          indexError,
        );
        // Fallback to simple query without ordering
        try {
          bookingQuery = query(bookingRef, where('dockId', 'in', batch));
          querySnapshot = await getDocs(bookingQuery);
        } catch (fallbackError) {
          console.error('Fallback query also failed:', fallbackError);
          continue; // Skip this batch if both queries fail
        }
      }

      querySnapshot.forEach((doc) => {
        const data = doc.data() as BookingData;

        // Format time slots for display
        const timeSlots =
          data.selectedSlotDetails?.map((slot: any) => {
            const startTime = slot.startTime?.toDate?.() || new Date();
            const endTime = slot.endTime?.toDate?.() || new Date();
            return `${startTime.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            })} - ${endTime.toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: true,
            })}`;
          }) || [];

        // Handle multi-date bookings
        let bookingDate = data.availableDate?.toDate?.() || new Date();
        let dateDisplay = bookingDate.toLocaleDateString();

        if (data.selectedDates && data.selectedDates.length > 0) {
          // Multi-date booking
          const sortedDates = data.selectedDates.sort();
          bookingDate = new Date(sortedDates[0]); // Use first date as primary

          if (sortedDates.length === 1) {
            dateDisplay = new Date(sortedDates[0]).toLocaleDateString();
          } else {
            const firstDate = new Date(sortedDates[0]).toLocaleDateString(
              'en-US',
              { month: 'short', day: '2-digit' },
            );
            const lastDate = new Date(
              sortedDates[sortedDates.length - 1],
            ).toLocaleDateString('en-US', {
              month: 'short',
              day: '2-digit',
              year: 'numeric',
            });
            dateDisplay = `${firstDate} - ${lastDate} (${sortedDates.length} dates)`;
          }
        }

        allBookings.push({
          id: doc.id,
          dockName: data.name,
          dockImage: data.dockImageUrl,
          bookingDate: bookingDate,
          dateDisplay: dateDisplay, // Add formatted date display
          timeSlots,
          totalPrice: data.totalPrice,
          status: data.status,
          customerName: data.customerDetails.fullName,
          customerContact: data.customerDetails.contact,
          numberOfSlots: data.numberOfSlots,
          numberOfDates: data.numberOfDates || 1,
          dockId: data.dockId,
        });
      });
    }

    // Sort by creation date (newest first)
    allBookings.sort(
      (a, b) => b.bookingDate.getTime() - a.bookingDate.getTime(),
    );

    return allBookings;
  } catch (error) {
    console.error('Error fetching owner dock bookings:', error);
    return [];
  }
};

// Check if a date is today or in the future
export const isFutureDate = (date: Date): boolean => {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day
  const compareDate = new Date(date);
  compareDate.setHours(0, 0, 0, 0); // Reset time to start of day
  return compareDate >= today;
};
