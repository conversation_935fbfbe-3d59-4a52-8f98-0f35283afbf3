import {
  collection,
  query,
  where,
  getDocs,
  addDoc,
  orderBy,
  Timestamp,
  doc,
  getDoc,
} from 'firebase/firestore';
import { db } from '@/config/firebase';

// Review data structure
export interface Review {
  id: string;
  dockId: string;
  userId: string;
  userName: string;
  userEmail: string;
  rating: number; // 1-5 stars
  comment: string;
  createdAt: Timestamp;
  bookingId: string; // Reference to the booking that allows this review
}

// Review statistics for a dock
export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
}

// Check if a user can add a review for a specific dock
export const canUserReviewDock = async (
  userId: string,
  dockId: string,
): Promise<{
  canReview: boolean;
  bookingId?: string;
  reason?: string;
}> => {
  try {
    // Check if user has a completed booking for this dock
    const bookingRef = collection(db, 'booking');
    const bookingQuery = query(
      bookingRef,
      where('bookingUserId', '==', userId),
      where('dockId', '==', dockId),
      where('status', '==', 'Approved'), // Only approved bookings can be reviewed
    );

    const bookingSnapshot = await getDocs(bookingQuery);

    if (bookingSnapshot.empty) {
      return {
        canReview: false,
        reason: 'You must have a completed booking to review this dock',
      };
    }

    // Check if user has already reviewed this dock
    const reviewRef = collection(db, 'reviews');
    const reviewQuery = query(
      reviewRef,
      where('userId', '==', userId),
      where('dockId', '==', dockId),
    );

    const reviewSnapshot = await getDocs(reviewQuery);

    if (!reviewSnapshot.empty) {
      return {
        canReview: false,
        reason: 'You have already reviewed this dock',
      };
    }

    // User can review - return the first booking ID
    const firstBooking = bookingSnapshot.docs[0];
    return {
      canReview: true,
      bookingId: firstBooking.id,
    };
  } catch (error) {
    console.error('Error checking review eligibility:', error);
    return {
      canReview: false,
      reason: 'Unable to verify review eligibility',
    };
  }
};

// Add a new review
export const addReview = async (reviewData: {
  dockId: string;
  userId: string;
  userName: string;
  userEmail: string;
  rating: number;
  comment: string;
  bookingId: string;
}): Promise<{ success: boolean; reviewId?: string; error?: string }> => {
  try {
    // Validate rating
    if (reviewData.rating < 1 || reviewData.rating > 5) {
      return {
        success: false,
        error: 'Rating must be between 1 and 5 stars',
      };
    }

    // Validate comment
    if (!reviewData.comment.trim()) {
      return {
        success: false,
        error: 'Review comment is required',
      };
    }

    // Check if user can review this dock
    const eligibility = await canUserReviewDock(
      reviewData.userId,
      reviewData.dockId,
    );
    if (!eligibility.canReview) {
      return {
        success: false,
        error: eligibility.reason || 'Cannot add review',
      };
    }

    // Add the review to Firebase
    const reviewRef = collection(db, 'reviews');
    const docRef = await addDoc(reviewRef, {
      ...reviewData,
      createdAt: Timestamp.now(),
    });

    return {
      success: true,
      reviewId: docRef.id,
    };
  } catch (error) {
    console.error('Error adding review:', error);
    return {
      success: false,
      error: 'Failed to add review. Please try again.',
    };
  }
};

// Get all reviews for a dock
export const getDockReviews = async (dockId: string): Promise<Review[]> => {
  try {
    const reviewRef = collection(db, 'reviews');

    // First try with ordering (requires composite index)
    let reviewQuery = query(
      reviewRef,
      where('dockId', '==', dockId),
      orderBy('createdAt', 'desc'),
    );

    let querySnapshot;
    try {
      querySnapshot = await getDocs(reviewQuery);
    } catch (indexError) {
      console.warn(
        'Composite index not available, falling back to simple query:',
        indexError,
      );
      // Fallback to simple query without ordering
      reviewQuery = query(reviewRef, where('dockId', '==', dockId));
      querySnapshot = await getDocs(reviewQuery);
    }

    const reviews: Review[] = [];
    querySnapshot.forEach((doc) => {
      reviews.push({
        id: doc.id,
        ...doc.data(),
      } as Review);
    });

    // Sort manually if we used the fallback query
    reviews.sort((a, b) => {
      const aTime = a.createdAt?.toDate?.() || new Date(0);
      const bTime = b.createdAt?.toDate?.() || new Date(0);
      return bTime.getTime() - aTime.getTime(); // Descending order (newest first)
    });

    return reviews;
  } catch (error) {
    console.error('Error fetching dock reviews:', error);
    return [];
  }
};

// Calculate review statistics for a dock
export const getDockReviewStats = async (
  dockId: string,
): Promise<ReviewStats> => {
  try {
    const reviews = await getDockReviews(dockId);

    if (reviews.length === 0) {
      return {
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
      };
    }

    // Calculate average rating
    const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
    const averageRating = totalRating / reviews.length;

    // Calculate rating distribution
    const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    reviews.forEach((review) => {
      ratingDistribution[review.rating as keyof typeof ratingDistribution]++;
    });

    return {
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal place
      totalReviews: reviews.length,
      ratingDistribution,
    };
  } catch (error) {
    console.error('Error calculating review stats:', error);
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 },
    };
  }
};

// Get user's review for a specific dock (if exists)
export const getUserReviewForDock = async (
  userId: string,
  dockId: string,
): Promise<Review | null> => {
  try {
    const reviewRef = collection(db, 'reviews');
    const reviewQuery = query(
      reviewRef,
      where('userId', '==', userId),
      where('dockId', '==', dockId),
    );

    const querySnapshot = await getDocs(reviewQuery);

    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    return {
      id: doc.id,
      ...doc.data(),
    } as Review;
  } catch (error) {
    console.error('Error fetching user review:', error);
    return null;
  }
};
