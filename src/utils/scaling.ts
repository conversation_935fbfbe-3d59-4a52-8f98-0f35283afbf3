import { Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

// Guideline sizes are based on standard ~5" screen mobile device
const guidelineBaseWidth = 350;
const guidelineBaseHeight = 680;

/**
 * Scale function for responsive design
 * @param size - The size to scale
 * @returns Scaled size based on device width
 */
const scale = (size: number): number => (width / guidelineBaseWidth) * size;

/**
 * Vertical scale function for responsive design
 * @param size - The size to scale vertically
 * @returns Scaled size based on device height
 */
const verticalScale = (size: number): number => (height / guidelineBaseHeight) * size;

/**
 * Moderate scale function for responsive design
 * Provides a balanced scaling that's not too aggressive
 * @param size - The size to scale
 * @param factor - Scaling factor (default: 0.5)
 * @returns Moderately scaled size
 */
const moderateScale = (size: number, factor = 0.5): number => 
  size + (scale(size) - size) * factor;

/**
 * Moderate vertical scale function for responsive design
 * @param size - The size to scale vertically
 * @param factor - Scaling factor (default: 0.5)
 * @returns Moderately scaled vertical size
 */
const moderateVerticalScale = (size: number, factor = 0.5): number => 
  size + (verticalScale(size) - size) * factor;

// Convenient aliases for easier usage
export const rm = moderateScale;
export const rmv = moderateVerticalScale;
export const rs = scale;
export const rv = verticalScale;

// Export all scaling functions
export {
  scale,
  verticalScale,
  moderateScale,
  moderateVerticalScale,
  width as screenWidth,
  height as screenHeight,
  guidelineBaseWidth,
  guidelineBaseHeight,
};

// Default export for the most commonly used function
export default moderateScale;
