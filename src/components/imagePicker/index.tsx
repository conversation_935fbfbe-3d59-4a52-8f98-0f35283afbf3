import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
  Platform,
  PermissionsAndroid,
  ActivityIndicator, // Still useful if you have local processing, but less critical now
} from 'react-native';
import ImagePicker from 'react-native-image-crop-picker';

// No Firebase imports needed for local display
// import {
//   getStorage,
//   ref,
//   uploadBytesResumable,
//   getDownloadURL,
// } from 'firebase/storage';
// import { app, auth } from '@/config/firebase';


// Define the structure for a selected image. firebaseUrl is no longer needed.
interface SelectedImage {
  uri: string;
  fileName?: string;
  type?: string;
}

interface ImageGalleryProps {
  selectedImages: SelectedImage[];
  setSelectedImages: (images: SelectedImage[]) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  selectedImages,
  setSelectedImages,
}) => {
  // uploading state might still be useful for a brief period while picker processes
  const [processingImage, setProcessingImage] = useState(false);
  const selectionLimit = 4;

  const requestAndroidPermissions = async () => {
    if (Platform.OS !== 'android') return true;

    try {
      const permissions = [
        PermissionsAndroid.PERMISSIONS.CAMERA,
        PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE,
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      ];

      if (Platform.Version >= 33) {
        permissions.push(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES);
      }

      const granted = await PermissionsAndroid.requestMultiple(permissions);
      const allGranted = permissions.every(
        (perm) => granted[perm as keyof typeof granted] === PermissionsAndroid.RESULTS.GRANTED
      );

      if (!allGranted) {
        Alert.alert('Permission Denied', 'Camera/storage permissions are required.');
      }

      return allGranted;
    } catch (err) {
      console.warn('Permission error:', err);
      return false;
    }
  };

  // This function is no longer strictly necessary for local display,
  // but keeping it as it's good practice for filenames if you ever add processing.
  const sanitizeFileName = (name: string) =>
    name.replace(/[#[\]*?]/g, '_').replace(/\s+/g, '_');


  // No more uploadImageToFirebase function as we're not uploading

  const handleChoosePhoto = async () => {
    const hasPermission = await requestAndroidPermissions();
    if (!hasPermission || processingImage) return;

    // No authentication check needed as we're not interacting with Firebase Auth

    Alert.alert('Select Image', 'Choose an option', [
      {
        text: 'Choose from Gallery',
        onPress: async () => {
          try {
            const images = await ImagePicker.openPicker({
              mediaType: 'photo',
              quality: 1,
              cropping: true,
              multiple: true,
              maxFiles: selectionLimit - selectedImages.length,
              compressImageQuality: 0.6
            });

            const imageArray = Array.isArray(images) ? images : [images];
            if (!imageArray.length) return;

            setProcessingImage(true); // Indicate that we're processing the selection
            const newLocalImages: SelectedImage[] = [];

            for (const image of imageArray) {
              // Extract basic info for local display
              const fileName =
                image.filename || image.path.split('/').pop() || `image_${Date.now()}`;
              const mimeType = image.mime || 'image/jpeg';

              newLocalImages.push({
                uri: image.path,
                fileName,
                type: mimeType,
              });
            }

            setSelectedImages((prev) => [...prev, ...newLocalImages]);

          } catch (error: any) {
            const message = error?.message || 'Unknown error';
            if (!message.toLowerCase().includes('cancelled')) {
              Alert.alert('Picker Error', message);
              console.log("Message from ImagePicker:", message);
            }
          } finally {
            setProcessingImage(false); // Done processing
          }
        },
      },
      {
        text: 'Take Photo',
        onPress: async () => {
          if (selectedImages.length >= selectionLimit) {
            Alert.alert('Limit Reached', `Max ${selectionLimit} images allowed.`);
            return;
          }

          // No authentication check needed

          try {
            setProcessingImage(true); // Indicate processing
            const image = await ImagePicker.openCamera({
              mediaType: 'photo',
              quality: 1,
              cropping: true,
              compressImageQuality: 0.6
            });

            const fileName =
              image.filename || image.path.split('/').pop() || `image_${Date.now()}`;
            const mimeType = image.mime || 'image/jpeg';

            setSelectedImages((prev) => [
              ...prev,
              {
                uri: image.path,
                fileName,
                type: mimeType,
              },
            ]);
          } catch (error: any) {
            const message = error?.message || 'Camera error';
            if (!message.toLowerCase().includes('cancelled')) {
              Alert.alert('Camera Error', message);
            }
          } finally {
            setProcessingImage(false); // Done processing
          }
        },
      },
      { text: 'Cancel', style: 'cancel' },
    ]);
  };

  const handleRemoveImage = (uriToRemove: string) => {
    setSelectedImages((prev) => prev.filter((img) => img.uri !== uriToRemove));
    // No Firebase deletion logic here
  };

  return (
    <View style={styles.container}>
      <Text style={styles.header}>Images</Text>
      <View style={styles.imageGrid}>
        {selectedImages.map((image, index) => (
          <View key={index} style={styles.imageWrapper}>
            <Image source={{ uri: image.uri }} style={styles.image} />
            <TouchableOpacity
              style={styles.removeButton}
              onPress={() => handleRemoveImage(image.uri)}
            >
              <Text style={styles.removeButtonText}>×</Text>
            </TouchableOpacity>
          </View>
        ))}
        {selectedImages.length < selectionLimit && (
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleChoosePhoto}
            disabled={processingImage} // Use processingImage
          >
            {processingImage ? ( // Use processingImage
              <ActivityIndicator size="small" color="#aaa" />
            ) : (
              <Text style={styles.addButtonText}>+</Text>
            )}
          </TouchableOpacity>
        )}
      </View>
      {/* Optional: You can remove this or change the text if you only have local processing */}
      {processingImage && (
        <Text style={styles.uploadingStatusText}>Processing image...</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 20,
    marginTop: 20,
  },
  header: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 20,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'flex-start',
  },
  imageWrapper: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 15,
    overflow: 'hidden',
    margin: 8,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: 5,
    right: 5,
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: 15,
    width: 25,
    height: 25,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  removeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  addButton: {
    width: 100,
    height: 100,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#DDDDDD',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 8,
    backgroundColor: '#f9f9f9',
  },
  addButtonText: {
    fontSize: 40,
    color: '#aaa',
    fontWeight: '300',
  },
  uploadingStatusText: {
    marginTop: 10,
    textAlign: 'center',
    color: '#3674B5',
    fontWeight: '500',
  },
});

export default ImageGallery;