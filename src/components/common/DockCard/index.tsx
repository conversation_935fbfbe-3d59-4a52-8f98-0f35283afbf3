
import { navigationRef } from '@/config/navigationRef';
import { Paths } from '@/navigation/paths';
import React, { useEffect, useState, useMemo } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, Dimensions } from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { checkDockBookingStatus, BookingStatus } from '@/utils/bookingUtils';
import { getDockReviewStats, ReviewStats } from '@/utils/reviewUtils';

const { width } = Dimensions.get('window');

interface TimeSlot {
    id: string;
    startTime: {
        seconds: number;
        nanoseconds: number;
    };
    endTime: {
        seconds: number;
        nanoseconds: number;
    };
}

interface Dock {
    id: string;
    imageUrl: string;
    name: string;
    description: string;
    // Only use dateTimeSlots
    dateTimeSlots?: { [date: string]: TimeSlot[] };
    // For display purposes (calculated from dateTimeSlots)
    availableDate?: Date | null;
    price: string;
}

interface DockCardProps {
    dock: Dock;
    onDelete?: (id: string) => void;
    onEdit?: () => void;
}

const DockCard: React.FC<DockCardProps> = ({
    dock,
    onDelete,
    onEdit,
}) => {
    const [bookingStatus, setBookingStatus] = useState<BookingStatus>({
        isBooked: false,
        bookedSlots: [],
        availableSlots: [],
        totalSlots: 0,
        remainingSlots: 0
    });

    const [reviewStats, setReviewStats] = useState<ReviewStats>({
        averageRating: 0,
        totalReviews: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    });

    const [loadingRating, setLoadingRating] = useState<boolean>(true);

    // Helper function to format timestamp
    const formatTimestamp = (timestamp: any) => {
        if (!timestamp || typeof timestamp.seconds !== 'number') return null;
        return new Date(timestamp.seconds * 1000 + (timestamp.nanoseconds || 0) / 1e6);
    };

    // Format date display from dateTimeSlots
    const formattedDate = useMemo(() => {
        if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
            const availableDates = Object.keys(dock.dateTimeSlots).sort();

            if (availableDates.length === 1) {
                return new Date(availableDates[0] + 'T12:00:00').toLocaleDateString();
            } else if (availableDates.length > 1) {
                const firstDate = new Date(availableDates[0] + 'T12:00:00').toLocaleDateString('en-US', { month: 'short', day: '2-digit' });
                const lastDate = new Date(availableDates[availableDates.length - 1] + 'T12:00:00').toLocaleDateString('en-US', { month: 'short', day: '2-digit' });
                return `${firstDate} - ${lastDate} (${availableDates.length} dates)`;
            }
        }

        // Fallback to passed availableDate if no dateTimeSlots
        return dock.availableDate ? dock.availableDate.toLocaleDateString() : 'No dates available';
    }, [dock.dateTimeSlots, dock.availableDate]);

    // Get time slots from dateTimeSlots - memoized to prevent infinite re-renders
    const timeSlots = useMemo(() => {
        // Get all time slots from all dates
        if (dock.dateTimeSlots && typeof dock.dateTimeSlots === 'object') {
            const allSlots: TimeSlot[] = [];
            Object.values(dock.dateTimeSlots).forEach(dateSlots => {
                if (Array.isArray(dateSlots)) {
                    allSlots.push(...dateSlots);
                }
            });
            return allSlots;
        }

        return [];
    }, [dock.dateTimeSlots]);

    // Fetch booking status and review stats
    useEffect(() => {
        const fetchData = async () => {
            // Fetch booking status
            if (timeSlots.length > 0) {
                const status = await checkDockBookingStatus(dock.id, timeSlots);
                setBookingStatus(status);
            }

            // Fetch review stats
            try {
                setLoadingRating(true);
                const stats = await getDockReviewStats(dock.id);
                setReviewStats(stats);
            } catch (error) {
                console.error('Error fetching review stats:', error);
            } finally {
                setLoadingRating(false);
            }
        };

        fetchData();
    }, [dock.id, timeSlots]);

    // Format time slots for display
    const formatTimeSlots = () => {
        if (timeSlots.length === 0) return 'No time slots available';

        // Get number of dates from dateTimeSlots
        const numberOfDates = dock.dateTimeSlots ? Object.keys(dock.dateTimeSlots).length : 1;

        // For multi-date docks, show total slots across all dates
        if (numberOfDates > 1) {
            return `${timeSlots.length} slots across ${numberOfDates} dates`;
        }

        // For single date docks
        if (timeSlots.length === 1) {
            const start = formatTimestamp(timeSlots[0].startTime);
            const end = formatTimestamp(timeSlots[0].endTime);
            const startTime = start ? start.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '--:--';
            const endTime = end ? end.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : '--:--';
            return `${startTime} - ${endTime}`;
        }

        return `${timeSlots.length} time slots available`;
    };



    return (
        <TouchableOpacity
            style={styles.card}
            onPress={() => navigationRef.navigate(Paths.ViewDock, {
                dockId: dock.id,
                source: 'dashboard'
            })}
            activeOpacity={0.95}
        >
            {/* Image Container with Gradient Overlay */}
            <View style={styles.imageContainer}>
                <Image
                    source={{ uri: dock.imageUrl || 'https://i0.wp.com/www.ecomena.org/wp-content/uploads/2020/02/enviroment-friendly-docks.jpg' }}
                    style={styles.image}
                    onError={(e) => console.log('Image loading error:', e.nativeEvent.error)}
                />

                {/* Gradient Overlay */}
                <View style={styles.imageGradient} />

                {/* Price Badge */}
                <View style={styles.priceBadge}>
                    <Text style={styles.priceText}>${dock.price || 0}</Text>
                    <Text style={styles.priceUnit}>/ slot</Text>
                </View>

                {/* Management Actions (Owner Only) */}
                {onDelete && (
                    <View style={styles.managementActions}>
                        <TouchableOpacity
                            onPress={(e) => {
                                e.stopPropagation();
                                onEdit && onEdit();
                            }}
                            style={[styles.actionButton, styles.editButton]}
                        >
                            <Icon name="pencil" size={16} color="#3674B5" />
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={(e) => {
                                e.stopPropagation();
                                onDelete(dock?.id);
                            }}
                            style={[styles.actionButton, styles.deleteButton]}
                        >
                            <Icon name="trash" size={16} color="#DC3545" />
                        </TouchableOpacity>
                    </View>
                )}
            </View>

            {/* Content Area */}
            <View style={styles.content}>
                {/* Header */}
                <View style={styles.header}>
                    <Text style={styles.dockName} numberOfLines={2}>{dock.name}</Text>
                    {loadingRating ? (
                        <View style={styles.ratingContainer}>
                            <Icon name="star-outline" size={14} color="#DEE2E6" />
                            <Text style={styles.noRatingText}>Loading...</Text>
                        </View>
                    ) : reviewStats.totalReviews > 0 ? (
                        <View style={styles.ratingContainer}>
                            <Icon name="star" size={14} color="#FFD700" />
                            <Text style={styles.ratingText}>
                                {reviewStats.averageRating.toFixed(1)}
                            </Text>
                            <Text style={styles.reviewCount}>
                                ({reviewStats.totalReviews})
                            </Text>
                        </View>
                    ) : (
                        <View style={styles.ratingContainer}>
                            <Icon name="star-outline" size={14} color="#DEE2E6" />
                            <Text style={styles.noRatingText}>No reviews</Text>
                        </View>
                    )}
                </View>

                {/* Description */}
                <Text style={styles.description} numberOfLines={2} ellipsizeMode="tail">
                    {dock.description || 'Beautiful dock with great amenities and stunning views.'}
                </Text>

                {/* Details Row */}
                <View style={styles.detailsRow}>
                    {/* Date */}
                    <View style={styles.detailItem}>
                        <Icon name="calendar-outline" size={14} color="#6C757D" />
                        <Text style={styles.detailText}>{formattedDate}</Text>
                    </View>

                    {/* Time Slots */}
                    <View style={styles.detailItem}>
                        <Icon name="time-outline" size={14} color="#6C757D" />
                        <Text style={styles.detailText}>{formatTimeSlots()}</Text>
                    </View>
                </View>

                {/* Bottom Row */}
                <View style={styles.bottomRow}>
                    <View style={styles.availabilityContainer}>
                        <View style={[
                            styles.availabilityBadge,
                            bookingStatus.isBooked && styles.notAvailableBadge
                        ]}>
                            <View style={[
                                styles.availabilityDot,
                                bookingStatus.isBooked && styles.notAvailableDot
                            ]} />
                            <Text style={[
                                styles.availabilityText,
                                bookingStatus.isBooked && styles.notAvailableText
                            ]}>
                                {bookingStatus.isBooked ? 'Not Available' : 'Available'}
                            </Text>
                        </View>

                        {/* Show remaining slots if partially booked */}
                        {!bookingStatus.isBooked && bookingStatus.remainingSlots < bookingStatus.totalSlots && bookingStatus.totalSlots > 0 && (
                            <Text style={styles.remainingSlotsText}>
                                {bookingStatus.remainingSlots} of {bookingStatus.totalSlots} slots left
                            </Text>
                        )}
                    </View>

                    <TouchableOpacity
                        style={styles.viewButton}
                        onPress={() => navigationRef.navigate(Paths.ViewDock, {
                            dockId: dock.id,
                            source: 'dashboard'
                        })}
                    >
                        <Text style={styles.viewButtonText}>View Details</Text>
                        <Icon name="arrow-forward" size={14} color="#3674B5" />
                    </TouchableOpacity>
                </View>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: '#FFFFFF',
        borderRadius: 20,
        marginBottom: 16,
        marginHorizontal: 16,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 8 },
        shadowOpacity: 0.12,
        shadowRadius: 24,
        elevation: 8,
        overflow: 'hidden',
        borderWidth: 2,
        borderColor: '#E3F2FD',
    },
    imageContainer: {
        position: 'relative',
        height: 200,
        width: '100%',
    },
    image: {
        width: '100%',
        height: '100%',
        resizeMode: 'cover',
    },
    imageGradient: {
        position: 'absolute',
        bottom: 0,
        left: 0,
        right: 0,
        height: 80,
        backgroundColor: 'rgba(0,0,0,0.3)',
    },
    priceBadge: {
        position: 'absolute',
        top: 16,
        right: 16,
        backgroundColor: 'rgba(54, 116, 181, 0.95)',
        borderRadius: 12,
        paddingHorizontal: 12,
        paddingVertical: 6,
        flexDirection: 'row',
        alignItems: 'baseline',
    },
    priceText: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#FFFFFF',
    },
    priceUnit: {
        fontSize: 12,
        color: '#FFFFFF',
        opacity: 0.9,
        marginLeft: 2,
    },
    managementActions: {
        position: 'absolute',
        top: 16,
        left: 16,
        flexDirection: 'row',
        gap: 8,
    },
    actionButton: {
        width: 36,
        height: 36,
        borderRadius: 18,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    editButton: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
    },
    deleteButton: {
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
    },
    content: {
        padding: 20,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: 8,
    },
    dockName: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#2C3E50',
        flex: 1,
        marginRight: 12,
        lineHeight: 24,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FFF8E1',
        borderRadius: 12,
        paddingHorizontal: 8,
        paddingVertical: 4,
    },
    ratingText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#F57C00',
        marginLeft: 4,
    },
    reviewCount: {
        fontSize: 11,
        color: '#6C757D',
        marginLeft: 2,
    },
    noRatingText: {
        fontSize: 11,
        color: '#6C757D',
        marginLeft: 4,
    },
    description: {
        fontSize: 14,
        color: '#6C757D',
        lineHeight: 20,
        marginBottom: 16,
    },
    detailsRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 16,
    },
    detailItem: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    detailText: {
        fontSize: 12,
        color: '#6C757D',
        marginLeft: 6,
        flex: 1,
    },
    bottomRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    availabilityBadge: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#E8F5E8',
        borderRadius: 16,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    availabilityDot: {
        width: 6,
        height: 6,
        borderRadius: 3,
        backgroundColor: '#28A745',
        marginRight: 6,
    },
    availabilityText: {
        fontSize: 12,
        fontWeight: '600',
        color: '#28A745',
    },
    viewButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#E3F2FD',
        borderRadius: 16,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
    viewButtonText: {
        fontSize: 14,
        fontWeight: '600',
        color: '#3674B5',
        marginRight: 6,
    },
    // Booking Status Styles
    availabilityContainer: {
        flex: 0,
    },
    notAvailableBadge: {
        backgroundColor: '#FFEBEE',
    },
    notAvailableDot: {
        backgroundColor: '#DC3545',
    },
    notAvailableText: {
        color: '#DC3545',
    },
    remainingSlotsText: {
        fontSize: 10,
        color: '#FF8F00',
        fontWeight: '500',
        marginTop: 4,
        marginLeft: 8
    },
});

export default DockCard;
