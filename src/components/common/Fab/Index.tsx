import * as React from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { FAB } from 'react-native-paper';
// import Icon from '@react-native-vector-icons/material-design-icons';



type FabProps = {
    onPressed: () => void;
};

const Fab: React.FC<FabProps> = ({ onPressed }) => (
    <FAB
        icon={() => <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}><Text style={{ fontSize: 24, fontWeight: "bold", alignItems: "center" }}>+</Text></View>}
        style={styles.fab}
        onPress={onPressed}
    />

);

const styles = StyleSheet.create({
    fab: {
        position: 'absolute',
        // margin: 16,
        right: 20,
        bottom: 20,
    },
})

export default Fab;