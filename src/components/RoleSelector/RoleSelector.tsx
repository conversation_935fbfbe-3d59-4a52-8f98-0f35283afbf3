import React from 'react';
import { ScrollView, Text, TouchableOpacity, View, Dimensions } from 'react-native';
import { AssetByVariant } from '@/components/atoms';
import { SafeScreen } from '@/components/templates';
import { useTheme } from '@/theme';
import Icon from 'react-native-vector-icons/Ionicons';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface RoleSelectorProps {
    onRoleSelect: (role: 'owner' | 'customer') => void;
}

function RoleSelector({ onRoleSelect }: RoleSelectorProps) {
    const { layout, backgrounds, colors, fonts } = useTheme();

    return (
        <ScrollView
            contentContainerStyle={{
                flexGrow: 1,
                justifyContent: 'center',
                // paddingHorizontal: 20,
                // paddingVertical: 40,
            }}
            bounces={false}
            keyboardShouldPersistTaps="handled"
            showsVerticalScrollIndicator={false}
        >
            <View style={[
                layout.flex_1,
                layout.col,
                layout.justifyCenter,
                backgrounds.white,
                { minHeight: screenHeight * 0.8, paddingHorizontal: 20 },

            ]}>
                <AssetByVariant
                    path="Logo"
                    resizeMode="contain"
                    style={{
                        marginBottom: 40,
                        alignSelf: "center",
                        width: screenWidth * 0.6,
                        height: 80
                    }}
                />

                <Text style={[
                    fonts.size_24,
                    fonts.bold,
                    {
                        color: colors.gray800,
                        textAlign: 'center',
                        marginBottom: 8
                    }
                ]}>
                    Welcome!
                </Text>

                <Text style={[
                    fonts.size_16,
                    {
                        color: colors.gray500,
                        textAlign: 'center',
                        marginBottom: 40,
                        lineHeight: 22
                    }
                ]}>
                    Choose your role to get started
                </Text>

                <TouchableOpacity
                    onPress={() => onRoleSelect('owner')}
                    style={{
                        marginVertical: 12,
                        borderRadius: 12,
                        height: 120,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 2,
                        borderColor: "#3674B5",
                        backgroundColor: 'rgba(54, 116, 181, 0.05)',
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                        elevation: 3,
                    }}
                    activeOpacity={0.8}
                >
                    <Icon name="business-outline" size={32} color="#3674B5" style={{ marginBottom: 8 }} />
                    <Text style={[
                        fonts.size_20,
                        fonts.bold,
                        { color: "#3674B5", textAlign: 'center' }
                    ]}>
                        Dock Owner
                    </Text>
                    <Text style={[
                        fonts.size_14,
                        { color: colors.gray500, textAlign: 'center', marginTop: 4 }
                    ]}>
                        Manage and rent out your docks
                    </Text>
                </TouchableOpacity>

                <TouchableOpacity
                    onPress={() => onRoleSelect('customer')}
                    style={{
                        marginVertical: 12,
                        borderRadius: 12,
                        height: 120,
                        justifyContent: 'center',
                        alignItems: 'center',
                        borderWidth: 2,
                        borderColor: "#3674B5",
                        backgroundColor: 'rgba(54, 116, 181, 0.05)',
                        shadowColor: '#000',
                        shadowOffset: {
                            width: 0,
                            height: 2,
                        },
                        shadowOpacity: 0.1,
                        shadowRadius: 4,
                        elevation: 3,
                    }}
                    activeOpacity={0.8}
                >
                    <Icon name="boat-outline" size={32} color="#3674B5" style={{ marginBottom: 8 }} />
                    <Text style={[
                        fonts.size_20,
                        fonts.bold,
                        { color: "#3674B5", textAlign: 'center' }
                    ]}>
                        Customer
                    </Text>
                    <Text style={[
                        fonts.size_14,
                        { color: colors.gray500, textAlign: 'center', marginTop: 4 }
                    ]}>
                        Find and book dock spaces
                    </Text>
                </TouchableOpacity>
            </View>
        </ScrollView>
    );
}

export default RoleSelector;
