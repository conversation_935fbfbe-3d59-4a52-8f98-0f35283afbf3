import React from 'react';
import {
    StyleSheet,
    View,
    Text,
    Image,
    TouchableOpacity,
} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';
import { BookingDisplay } from '@/utils/bookingUtils';
import { rm } from '@/utils/scaling';

const Colors = {
    PrimaryColor: '#3674B5',
    AccentColor: '#ffc107',
    Gray: '#6c757d',
    LightGray: '#f8f9fa',
    White: '#ffffff',
    Dark: '#343a40',
    Heading: '#212529',
    Border: '#dee2e6',
    Success: '#28a745',
    Warning: '#ffc107',
    Error: '#dc3545',
    Info: '#17a2b8',
};

interface BookingCardProps {
    booking: BookingDisplay;
    isOwnerView?: boolean;
    onPress?: () => void;
    onContactPress?: () => void;
}

const BookingCard: React.FC<BookingCardProps> = ({
    booking,
    isOwnerView = false,
    onPress,
    onContactPress,
}) => {
    const getStatusColor = (status: string) => {
        switch (status.toLowerCase()) {
            case 'approved':
            case 'completed':
                return Colors.Success;
            case 'pending':
                return Colors.Warning;
            case 'cancelled':
                return Colors.Error;
            default:
                return Colors.Gray;
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status.toLowerCase()) {
            case 'approved':
                return 'checkmark-circle';
            case 'completed':
                return 'checkmark-done-circle';
            case 'pending':
                return 'time';
            case 'cancelled':
                return 'close-circle';
            default:
                return 'help-circle';
        }
    };

    const formatDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
        });
    };

    return (
        <TouchableOpacity
            style={styles.card}
            onPress={onPress}
            activeOpacity={0.7}
        >
            {/* Dock Image and Basic Info */}
            <View style={styles.header}>
                <Image
                    source={{ uri: booking.dockImage || 'https://i0.wp.com/www.ecomena.org/wp-content/uploads/2020/02/enviroment-friendly-docks.jpg' }}
                    style={styles.dockImage}
                    resizeMode="cover"
                />
                <View style={styles.headerInfo}>
                    <Text style={styles.dockName} numberOfLines={1}>
                        {booking.dockName}
                    </Text>
                    <Text style={styles.bookingDate}>
                        {formatDate(booking.bookingDate)}
                    </Text>
                    <View style={styles.statusContainer}>
                        <Icon
                            name={getStatusIcon(booking.status)}
                            size={16}
                            color={getStatusColor(booking.status)}
                        />
                        <Text style={[styles.statusText, { color: getStatusColor(booking.status) }]}>
                            {booking.status}
                        </Text>
                    </View>
                </View>
                <View style={styles.priceContainer}>
                    <Text style={styles.priceText}>
                        ${booking.totalPrice.toFixed(2)}
                    </Text>
                    <Text style={styles.slotsText}>
                        {booking.numberOfSlots} slot{booking.numberOfSlots !== 1 ? 's' : ''}
                    </Text>
                </View>
            </View>

            {/* Time Slots */}
            <View style={styles.timeSlotsSection}>
                <Text style={styles.sectionLabel}>Time Slots:</Text>
                <View style={styles.timeSlotsContainer}>
                    {booking.timeSlots.slice(0, 2).map((slot, index) => (
                        <View key={index} style={styles.timeSlotChip}>
                            <Icon name="time-outline" size={12} color={Colors.PrimaryColor} />
                            <Text style={styles.timeSlotText}>{slot}</Text>
                        </View>
                    ))}
                    {booking.timeSlots.length > 2 && (
                        <View style={styles.moreSlots}>
                            <Text style={styles.moreSlotsText}>
                                +{booking.timeSlots.length - 2} more
                            </Text>
                        </View>
                    )}
                </View>
            </View>

            {/* Customer Info (Owner View) or Booking ID (Customer View) */}
            {isOwnerView ? (
                <View style={styles.customerSection}>
                    <View style={styles.customerInfo}>
                        <Icon name="person-outline" size={16} color={Colors.Gray} />
                        <Text style={styles.customerName}>{booking.customerName}</Text>
                    </View>
                    <TouchableOpacity
                        style={styles.contactButton}
                        onPress={onContactPress}
                    >
                        <Icon name="call-outline" size={16} color={Colors.PrimaryColor} />
                        <Text style={styles.contactText}>Contact</Text>
                    </TouchableOpacity>
                </View>
            ) : (
                <View style={styles.bookingIdSection}>
                    <Text style={styles.bookingIdLabel}>Booking ID:</Text>
                    <Text style={styles.bookingIdText}>#{booking.id.slice(-8).toUpperCase()}</Text>
                </View>
            )}

            {/* Action Indicator */}
            <View style={styles.actionIndicator}>
                <Icon name="chevron-forward" size={20} color={Colors.Gray} />
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    card: {
        backgroundColor: Colors.White,
        borderRadius: rm(12),
        padding: rm(16),
        marginVertical: rm(8),
        marginHorizontal: rm(20),
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.1,
        shadowRadius: 3.84,
        elevation: 5,
        borderWidth: 1,
        borderColor: Colors.Border,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: rm(12),
    },
    dockImage: {
        width: rm(60),
        height: rm(60),
        borderRadius: rm(8),
        marginRight: rm(12),
    },
    headerInfo: {
        flex: 1,
    },
    dockName: {
        fontSize: rm(16),
        fontWeight: 'bold',
        color: Colors.Heading,
        marginBottom: rm(4),
    },
    bookingDate: {
        fontSize: rm(14),
        color: Colors.Gray,
        marginBottom: rm(6),
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    statusText: {
        fontSize: rm(12),
        fontWeight: '600',
        marginLeft: rm(4),
        textTransform: 'capitalize',
    },
    priceContainer: {
        alignItems: 'flex-end',
    },
    priceText: {
        fontSize: rm(18),
        fontWeight: 'bold',
        color: Colors.PrimaryColor,
    },
    slotsText: {
        fontSize: rm(12),
        color: Colors.Gray,
        marginTop: rm(2),
    },
    timeSlotsSection: {
        marginBottom: rm(12),
    },
    sectionLabel: {
        fontSize: rm(12),
        fontWeight: '600',
        color: Colors.Gray,
        marginBottom: rm(6),
    },
    timeSlotsContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignItems: 'center',
    },
    timeSlotChip: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.LightGray,
        borderRadius: rm(16),
        paddingHorizontal: rm(8),
        paddingVertical: rm(4),
        marginRight: rm(6),
        marginBottom: rm(4),
    },
    timeSlotText: {
        fontSize: rm(10),
        color: Colors.Dark,
        marginLeft: rm(4),
    },
    moreSlots: {
        backgroundColor: Colors.PrimaryColor,
        borderRadius: rm(16),
        paddingHorizontal: rm(8),
        paddingVertical: rm(4),
    },
    moreSlotsText: {
        fontSize: rm(10),
        color: Colors.White,
        fontWeight: '600',
    },
    customerSection: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: rm(8),
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        flex: 1,
    },
    customerName: {
        fontSize: rm(14),
        color: Colors.Dark,
        marginLeft: rm(6),
        fontWeight: '500',
    },
    contactButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: Colors.LightGray,
        borderRadius: rm(20),
        paddingHorizontal: rm(12),
        paddingVertical: rm(6),
    },
    contactText: {
        fontSize: rm(12),
        color: Colors.PrimaryColor,
        fontWeight: '600',
        marginLeft: rm(4),
    },
    bookingIdSection: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: rm(8),
    },
    bookingIdLabel: {
        fontSize: rm(12),
        color: Colors.Gray,
        fontWeight: '600',
    },
    bookingIdText: {
        fontSize: rm(12),
        color: Colors.Dark,
        fontWeight: 'bold',
        fontFamily: 'monospace',
    },
    actionIndicator: {
        position: 'absolute',
        right: rm(16),
        top: '50%',
        transform: [{ translateY: -10 }],
    },
});

export default BookingCard;
