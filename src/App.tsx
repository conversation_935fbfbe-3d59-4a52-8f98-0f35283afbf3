import 'react-native-gesture-handler'; // Keep this first

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { MMKV } from 'react-native-mmkv';
import React from 'react'; // <--- Only import React
import { AuthProvider } from '@/config/AuthProvider'; // Import AuthProvider if needed, but not used in this version

import ApplicationNavigator from '@/navigation/Application';
import { ThemeProvider } from '@/theme';
import '@/translations';

export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      retry: false,
    },
    queries: {
      retry: false,
    },
  },
});

export const storage = new MMKV(); // Still useful for app-wide settings or caching read-only data

function App() {
  // --- All authentication state management (useState, useEffect, auth import) is removed ---
  // --- No AuthProvider or direct auth logic here ---

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <ThemeProvider storage={storage}>
            {/* <--- ApplicationNavigator is rendered directly without auth props --- */}
            <ApplicationNavigator />
          </ThemeProvider>
        </AuthProvider>
      </QueryClientProvider>
    </GestureHandlerRootView>
  );
}

export default App;