# Firebase Firestore Indexes Required

## Reviews Collection Index

The review system requires a composite index for optimal performance when querying reviews by dock and ordering by creation date.

### Required Index Configuration:

**Collection**: `reviews`
**Fields**:

1. `dockId` (Ascending)
2. `createdAt` (Descending)

### How to Create the Index:

#### Option 1: Automatic (Recommended)

1. Click this link to automatically create the index:
   [Create Reviews Index](https://console.firebase.google.com/v1/r/project/dawson-lone-launcher/firestore/indexes?create_composite=ClRwcm9qZWN0cy9kYXdzb24tbG9uZS1sYXVuY2hlci9kYXRhYmFzZXMvKGRlZmF1bHQpL2NvbGxlY3Rpb25Hcm91cHMvcmV2aWV3cy9pbmRleGVzL18QARoKCgZkb2NrSWQQARoNCgljcmVhdGVkQXQQAhoMCghfX25hbWVfXxAC)

2. Click "Create Index" in the Firebase Console
3. Wait for the index to build (usually takes a few minutes)

#### Option 2: Manual Creation

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `dawson-lone-launcher`
3. Navigate to Firestore Database → Indexes
4. Click "Create Index"
5. Configure:
   - Collection ID: `reviews`
   - Field 1: `dockId` (Ascending)
   - Field 2: `createdAt` (Descending)
6. Click "Create"

### Index Status

- ⏳ **Building**: Index is being created (can take several minutes)
- ✅ **Ready**: Index is active and queries will work optimally
- ❌ **Error**: Check configuration and try again

### Fallback Behavior

The app includes fallback logic that will:

1. Try the optimized query with ordering first
2. If the index doesn't exist, fall back to a simple query
3. Sort results manually in the app
4. Continue working while the index is being built

### Performance Impact

- **With Index**: Fast queries, optimal performance
- **Without Index**: Slower queries, manual sorting, but still functional

### Verification

Once the index is created, you should see:

- No more "requires an index" errors in the console
- Faster loading of reviews
- Proper chronological ordering of reviews

## Other Potential Indexes

If you plan to add more complex queries in the future, consider these indexes:

### User Reviews Index

- Collection: `reviews`
- Fields: `userId` (Ascending), `createdAt` (Descending)
- Use case: Show all reviews by a specific user

### Rating Filter Index

- Collection: `reviews`
- Fields: `dockId` (Ascending), `rating` (Descending)
- Use case: Filter reviews by rating for a dock

### Booking Reviews Index

- Collection: `reviews`
- Fields: `bookingId` (Ascending)
- Use case: Link reviews to specific bookings

## Booking Collection Indexes

The booking management system requires additional composite indexes for optimal performance.

### Customer Bookings Index

- **Collection**: `booking`
- **Fields**:
  1. `bookingUserId` (Ascending)
  2. `createdAt` (Descending)
- **Use case**: Fetch all bookings for a specific customer, ordered by creation date

### Owner Dock Bookings Index

- **Collection**: `booking`
- **Fields**:
  1. `dockId` (Ascending)
  2. `createdAt` (Descending)
- **Use case**: Fetch all bookings for specific docks owned by a user, ordered by creation date

### How to Create Booking Indexes

#### Option 1: Automatic Creation

When you encounter the index error, Firebase will provide a direct link to create the required index. Click the link and then "Create Index".

#### Option 2: Manual Creation

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `dawson-lone-launcher`
3. Navigate to Firestore Database → Indexes
4. Click "Create Index"
5. Configure each index as specified above

### Fallback Behavior

The booking system includes fallback logic that will:

1. Try the optimized query with ordering first
2. If the index doesn't exist, fall back to a simple query
3. Sort results manually in the app
4. Continue working while the index is being built

This ensures the app remains functional even without the indexes, though performance will be slower until they are created.
